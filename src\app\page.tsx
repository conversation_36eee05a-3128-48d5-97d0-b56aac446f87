'use client';

import { useState, useRef, useEffect } from 'react';
import { RobotFace } from '@/components/RobotFace';
import { EmotionControls } from '@/components/EmotionControls';
import { PermissionButton } from '@/components/PermissionButton';
import { CaptureButton } from '@/components/CaptureButton';
import { SoundControls } from '@/components/SoundControls';
import { WebcamCapture } from '@/components/WebcamCapture';
import { useShake } from '@/hooks/useShake';
import { getRandomEmotion } from '@/lib/emotionLogic';
import { playEmotionSound, playShakeSound, playStartupSound } from '@/lib/soundEffects';
import { EmotionType } from '@/utils/emotionTypes';

export default function Home() {
  const [currentEmotion, setCurrentEmotion] = useState<EmotionType>('neutral');
  const [isSoundEnabled, setIsSoundEnabled] = useState(true);
  const [activeTab, setActiveTab] = useState<'robot' | 'camera'>('robot');
  const svgRef = useRef<SVGSVGElement | null>(null);

  // Play startup sound on component mount
  useEffect(() => {
    const playStartup = async () => {
      if (isSoundEnabled) {
        try {
          await playStartupSound();
        } catch (error) {
          console.error('Failed to play startup sound:', error);
        }
      }
    };
    
    // Delay to ensure user interaction has occurred
    const timer = setTimeout(playStartup, 1000);
    return () => clearTimeout(timer);
  }, [isSoundEnabled]);

  const handleEmotionChange = async (emotion: EmotionType) => {
    setCurrentEmotion(emotion);
    
    if (isSoundEnabled) {
      try {
        await playEmotionSound(emotion);
      } catch (error) {
        console.error('Failed to play emotion sound:', error);
      }
    }
  };

  const handleShake = async () => {
    const randomEmotion = getRandomEmotion();
    
    if (isSoundEnabled) {
      try {
        await playShakeSound();
      } catch (error) {
        console.error('Failed to play shake sound:', error);
      }
    }
    
    setCurrentEmotion(randomEmotion);
    
    // Play emotion sound after shake sound
    if (isSoundEnabled) {
      setTimeout(async () => {
        try {
          await playEmotionSound(randomEmotion);
        } catch (error) {
          console.error('Failed to play emotion sound:', error);
        }
      }, 300);
    }
  };

  const { isSupported, hasPermission, requestPermission } = useShake(handleShake);

  return (
    <div className="min-h-screen bg-black text-white" style={{ background: 'radial-gradient(circle at center, #0a0a0a 0%, #000000 100%)' }}>
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
            🤖 Vector-Style Robot Face
          </h1>
          <p className="text-gray-400">Futuristic expressive robot with OLED-optimized design</p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-gray-900 border border-gray-700 rounded-xl p-1 shadow-2xl" style={{ boxShadow: '0 0 20px rgba(0, 170, 255, 0.1)' }}>
            <button
              onClick={() => setActiveTab('robot')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'robot'
                  ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
              style={activeTab === 'robot' ? { boxShadow: '0 0 15px rgba(0, 170, 255, 0.4)' } : {}}
            >
              🤖 Robot Face
            </button>
            <button
              onClick={() => setActiveTab('camera')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'camera'
                  ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
              style={activeTab === 'camera' ? { boxShadow: '0 0 15px rgba(0, 170, 255, 0.4)' } : {}}
            >
              📸 Camera
            </button>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          {activeTab === 'robot' ? (
            <div className="grid lg:grid-cols-2 gap-8 items-start">
              {/* Robot Face */}
              <div className="flex flex-col items-center space-y-6">
                <div className="relative">
                  {/* Holographic frame effect */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 blur-xl animate-pulse"></div>
                  <div className="relative bg-gray-900/50 backdrop-blur-sm rounded-3xl p-8 border border-gray-700/50" 
                       style={{ boxShadow: '0 0 40px rgba(0, 170, 255, 0.15)' }}>
                    <RobotFace emotion={currentEmotion} className="mb-4" svgRef={svgRef} />
                  </div>
                </div>
                
                <div className="text-center bg-gray-900/30 backdrop-blur-sm rounded-xl p-4 border border-gray-700/30">
                  <p className="text-lg font-medium capitalize">
                    Current emotion: <span className="text-cyan-400 font-bold">{currentEmotion.replace('_', ' ')}</span>
                  </p>
                </div>

                {/* Photo Capture */}
                <CaptureButton emotion={currentEmotion} svgRef={svgRef} isSoundEnabled={isSoundEnabled} />

                {/* Shake Detection */}
                <PermissionButton
                  onRequestPermission={requestPermission}
                  hasPermission={hasPermission}
                  isSupported={isSupported}
                />
              </div>

              {/* Controls */}
              <div className="space-y-6">
                {/* Sound Controls */}
                <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 border border-gray-700/40" 
                     style={{ boxShadow: '0 0 20px rgba(0, 170, 255, 0.1)' }}>
                  <SoundControls
                    isSoundEnabled={isSoundEnabled}
                    onSoundToggle={setIsSoundEnabled}
                  />
                </div>
                
                <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 border border-gray-700/40" 
                     style={{ boxShadow: '0 0 20px rgba(0, 170, 255, 0.1)' }}>
                  <EmotionControls
                    currentEmotion={currentEmotion}
                    onEmotionChange={handleEmotionChange}
                  />
                </div>
              </div>
            </div>
          ) : (
            /* Webcam Tab */
            <div className="max-w-2xl mx-auto">
              <WebcamCapture isSoundEnabled={isSoundEnabled} />
            </div>
          )}
        </div>

        <footer className="text-center mt-12 text-gray-400 text-sm">
          <div className="bg-gray-900/20 backdrop-blur-sm rounded-xl p-4 border border-gray-700/20 inline-block">
            <p className="flex items-center justify-center gap-2">
              <span>📱</span>
              Shake your device (mobile) or use the controls to change emotions
              <span>🎮</span>
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}