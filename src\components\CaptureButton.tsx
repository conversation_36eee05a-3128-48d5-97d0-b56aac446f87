"use client";

import { useState } from "react";
import { captureRobotFace, shareRobotFace } from "@/lib/captureUtils";
import { playCaptureSound } from "@/lib/soundEffects";
import { EmotionType, EMOTIONS } from "@/utils/emotionTypes";

interface CaptureButtonProps {
  emotion: EmotionType;
  svgRef: React.RefObject<SVGSVGElement | null>;
  isSoundEnabled?: boolean;
}

export const CaptureButton = ({
  emotion,
  svgRef,
  isSoundEnabled = true,
}: CaptureButtonProps) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const handleCapture = async () => {
    if (!svgRef.current) return;

    setIsCapturing(true);

    // Play capture sound
    if (isSoundEnabled) {
      try {
        await playCaptureSound();
      } catch (error) {
        console.error("Failed to play capture sound:", error);
      }
    }

    try {
      await captureRobotFace(svgRef.current, EMOTIONS[emotion]);
    } catch (error) {
      console.error("Failed to capture:", error);
      alert("Failed to capture image. Please try again.");
    } finally {
      setIsCapturing(false);
    }
  };

  const handleShare = async () => {
    if (!svgRef.current) return;

    setIsSharing(true);
    try {
      await shareRobotFace(svgRef.current, EMOTIONS[emotion]);
    } catch (error) {
      console.error("Failed to share:", error);
      // Fallback to capture if sharing is not supported
      await handleCapture();
    } finally {
      setIsSharing(false);
    }
  };

  const isWebShareSupported =
    typeof navigator !== "undefined" && navigator.share;

  return (
    <div className="flex gap-4 justify-center">
      <button
        onClick={handleCapture}
        disabled={isCapturing}
        className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 border border-green-500/30"
        style={
          !isCapturing ? { boxShadow: "0 0 20px rgba(34, 197, 94, 0.3)" } : {}
        }
      >
        {isCapturing ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>Capturing...</span>
          </>
        ) : (
          <>
            <span className="text-lg">📸</span>
            <span>Download Photo</span>
          </>
        )}
      </button>

      {isWebShareSupported && (
        <button
          onClick={handleShare}
          disabled={isSharing}
          className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 border border-blue-500/30"
          style={
            !isSharing ? { boxShadow: "0 0 20px rgba(59, 130, 246, 0.3)" } : {}
          }
        >
          {isSharing ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Sharing...</span>
            </>
          ) : (
            <>
              <span className="text-lg">📤</span>
              <span>Share Photo</span>
            </>
          )}
        </button>
      )}
    </div>
  );
};
