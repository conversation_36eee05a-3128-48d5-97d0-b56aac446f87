'use client';

import { EmotionType, EMOTIONS, EMOTION_CATEGORIES } from '@/utils/emotionTypes';
import { mapTextToEmotion } from '@/lib/emotionLogic';
import { useState } from 'react';

interface EmotionControlsProps {
  currentEmotion: EmotionType;
  onEmotionChange: (emotion: EmotionType) => void;
}

export const EmotionControls = ({ currentEmotion, onEmotionChange }: EmotionControlsProps) => {
  const [inputText, setInputText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof EMOTION_CATEGORIES>('positive');

  const handleTextSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim()) {
      const emotion = mapTextToEmotion(inputText);
      onEmotionChange(emotion);
      setInputText('');
    }
  };

  const getCategoryColor = (category: keyof typeof EMOTION_CATEGORIES) => {
    switch (category) {
      case 'positive': return 'bg-green-600 hover:bg-green-700';
      case 'negative': return 'bg-red-600 hover:bg-red-700';
      case 'neutral': return 'bg-gray-600 hover:bg-gray-700';
      case 'reactive': return 'bg-yellow-600 hover:bg-yellow-700';
      case 'blink': return 'bg-purple-600 hover:bg-purple-700';
      case 'special': return 'bg-indigo-600 hover:bg-indigo-700';
      default: return 'bg-blue-600 hover:bg-blue-700';
    }
  };

  const getEmotionButtonColor = (emotion: EmotionType) => {
    if (currentEmotion === emotion) return 'bg-blue-600 text-white';
    
    // Find which category this emotion belongs to
    for (const [category, emotions] of Object.entries(EMOTION_CATEGORIES)) {
      if ((emotions as readonly EmotionType[]).includes(emotion)) {
        switch (category) {
          case 'positive': return 'bg-green-700 hover:bg-green-600 text-green-100';
          case 'negative': return 'bg-red-700 hover:bg-red-600 text-red-100';
          case 'neutral': return 'bg-gray-700 hover:bg-gray-600 text-gray-200';
          case 'reactive': return 'bg-yellow-700 hover:bg-yellow-600 text-yellow-100';
          case 'blink': return 'bg-purple-700 hover:bg-purple-600 text-purple-100';
          case 'special': return 'bg-indigo-700 hover:bg-indigo-600 text-indigo-100';
        }
      }
    }
    
    return 'bg-gray-700 hover:bg-gray-600 text-gray-200';
  };

  return (
    <div className="space-y-6">
      {/* Text input */}
      <form onSubmit={handleTextSubmit} className="space-y-4">
        <div>
          <label htmlFor="emotion-input" className="block text-sm font-medium text-cyan-300 mb-3">
            💬 Tell the robot how you feel:
          </label>
          <input
            id="emotion-input"
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="Try: 'I am very happy', 'feeling worried', 'super excited'..."
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500/50 backdrop-blur-sm transition-all duration-300"
            style={{ boxShadow: 'inset 0 0 10px rgba(0, 0, 0, 0.3)' }}
          />
        </div>
        <button
          type="submit"
          disabled={!inputText.trim()}
          className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02] disabled:hover:scale-100"
          style={{ boxShadow: !inputText.trim() ? 'none' : '0 0 20px rgba(0, 170, 255, 0.3)' }}
        >
          🚀 Express Emotion
        </button>
      </form>

      {/* Category selector */}
      <div>
        <p className="text-sm font-medium text-cyan-300 mb-4">🎭 Choose emotion category:</p>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-6">
          {Object.keys(EMOTION_CATEGORIES).map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category as keyof typeof EMOTION_CATEGORIES)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-300 capitalize transform hover:scale-105 ${
                selectedCategory === category
                  ? getCategoryColor(category as keyof typeof EMOTION_CATEGORIES) + ' shadow-lg'
                  : 'bg-gray-800/50 hover:bg-gray-700/50 text-gray-200 border border-gray-600/30'
              }`}
              style={selectedCategory === category ? { boxShadow: '0 0 15px rgba(0, 170, 255, 0.3)' } : {}}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Emotion buttons for selected category */}
      <div>
        <p className="text-sm font-medium text-cyan-300 mb-4">
          ⚡ {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} emotions:
        </p>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {EMOTION_CATEGORIES[selectedCategory].map((emotion) => (
            <button
              key={emotion}
              onClick={() => onEmotionChange(emotion)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-300 text-sm transform hover:scale-105 ${
                currentEmotion === emotion 
                  ? 'bg-gradient-to-r from-cyan-600 to-blue-600 text-white shadow-lg border-2 border-cyan-400' 
                  : getEmotionButtonColor(emotion) + ' border border-gray-600/30'
              }`}
              style={currentEmotion === emotion ? { boxShadow: '0 0 20px rgba(0, 255, 255, 0.4)' } : {}}
            >
              {EMOTIONS[emotion]}
            </button>
          ))}
        </div>
      </div>

      {/* Quick access to common emotions */}
      <div>
        <p className="text-sm font-medium text-cyan-300 mb-4">⚡ Quick access:</p>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          {(['neutral', 'happy', 'sad_down', 'angry'] as EmotionType[]).map((emotion) => (
            <button
              key={emotion}
              onClick={() => onEmotionChange(emotion)}
              className={`px-3 py-2 rounded-lg font-medium transition-all duration-300 text-sm transform hover:scale-105 ${
                currentEmotion === emotion 
                  ? 'bg-gradient-to-r from-cyan-600 to-blue-600 text-white shadow-lg' 
                  : 'bg-gray-800/50 hover:bg-gray-700/50 text-gray-200 border border-gray-600/30'
              }`}
              style={currentEmotion === emotion ? { boxShadow: '0 0 15px rgba(0, 255, 255, 0.3)' } : {}}
            >
              {EMOTIONS[emotion]}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};