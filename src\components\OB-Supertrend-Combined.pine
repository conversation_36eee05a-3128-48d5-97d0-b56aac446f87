// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @version=5
indicator("OB + Supertrend Combined", overlay=true, max_boxes_count=20, max_labels_count=20, max_lines_count=20)

// === INPUTS ===
Periods = input.int(10, title="ATR Period")
Multiplier = input.float(3.0, title="ATR Multiplier", step=0.1)
showSignals = input.bool(true, "Show Signals")
showOB = input.bool(true, "Show Order Blocks")

// === SUPERTREND CALCULATION ===
atr = ta.atr(Periods)
up = hl2 - (Multiplier * atr)
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = hl2 + (Multiplier * atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn

var int trend = 1
trend := nz(trend[1], 1)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// Supertrend signals
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1

// === SIMPLE ORDER BLOCKS ===
// Calculate on every bar
highest20 = ta.highest(high, 20)
lowest20 = ta.lowest(low, 20)

// Order Block variables
var float bullOB_top = na
var float bullOB_bottom = na
var float bearOB_top = na
var float bearOB_bottom = na
var bool bullOB_active = false
var bool bearOB_active = false

// Create bullish OB on strong upward breakout
strongUp = close > close[3] and high > highest20[1]
if strongUp
    bullOB_bottom := low[1]
    bullOB_top := high[1]
    bullOB_active := true

// Create bearish OB on strong downward breakout
strongDown = close < close[3] and low < lowest20[1]
if strongDown
    bearOB_top := high[1]
    bearOB_bottom := low[1]
    bearOB_active := true

// Invalidate OBs
if bullOB_active and close < bullOB_bottom
    bullOB_active := false

if bearOB_active and close > bearOB_top
    bearOB_active := false

// Check if in OB zones
inBullOB = bullOB_active and close >= bullOB_bottom and close <= bullOB_top
inBearOB = bearOB_active and close >= bearOB_bottom and close <= bearOB_top

// === COMBINED SIGNALS ===
buyAlert = buySignal and inBullOB
sellAlert = sellSignal and inBearOB

// === VISUALIZATION ===
// Supertrend lines
upPlot = plot(trend == 1 ? up : na, title="Up Trend", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot = plot(trend == -1 ? dn : na, title="Down Trend", style=plot.style_linebr, linewidth=2, color=color.red)

// Order Block boxes
var box bullBox = na
var box bearBox = na

if bullOB_active and showOB
    if na(bullBox)
        bullBox := box.new(bar_index-5, bullOB_top, bar_index+20, bullOB_bottom, 
                          bgcolor=color.new(color.green, 85), border_color=color.green)
    else
        box.set_right(bullBox, bar_index+20)

if not bullOB_active and not na(bullBox)
    box.delete(bullBox)
    bullBox := na

if bearOB_active and showOB
    if na(bearBox)
        bearBox := box.new(bar_index-5, bearOB_top, bar_index+20, bearOB_bottom, 
                          bgcolor=color.new(color.red, 85), border_color=color.red)
    else
        box.set_right(bearBox, bar_index+20)

if not bearOB_active and not na(bearBox)
    box.delete(bearBox)
    bearBox := na

// === SIGNAL VISUALIZATION ===
// Debug shapes - always show Supertrend signals
plotshape(buySignal, title="ST Buy", location=location.belowbar, style=shape.triangleup, size=size.tiny, color=color.blue)
plotshape(sellSignal, title="ST Sell", location=location.abovebar, style=shape.triangledown, size=size.tiny, color=color.orange)

// Background when in OB
bgcolor(inBullOB ? color.new(color.green, 95) : na, title="In Bull OB")
bgcolor(inBearOB ? color.new(color.red, 95) : na, title="In Bear OB")

// Combined signals
if buyAlert and showSignals
    // Calculate target and risk
    entryPrice = close
    slPrice = up
    riskDistance = math.abs(entryPrice - slPrice)
    targetPrice = entryPrice + (riskDistance * 2)
    rrRatio = 2.0
    
    // Signal box
    box.new(bar_index, entryPrice + riskDistance*0.3, bar_index + 8, entryPrice - riskDistance*0.3, 
           bgcolor=color.new(color.green, 80), border_color=color.green, border_width=2)
    
    // Signal label
    label.new(bar_index, entryPrice, 
             "BUY SIGNAL\nEntry: " + str.tostring(entryPrice, "#.##") + 
             "\nTarget: " + str.tostring(targetPrice, "#.##") + 
             "\nSL: " + str.tostring(slPrice, "#.##") + 
             "\nR/R: " + str.tostring(rrRatio, "#.##"), 
             color=color.green, textcolor=color.white, style=label.style_label_center, size=size.small)
    
    // Target and SL lines
    line.new(bar_index, targetPrice, bar_index + 20, targetPrice, color=color.green, style=line.style_solid, width=2)
    line.new(bar_index, slPrice, bar_index + 20, slPrice, color=color.red, style=line.style_dashed, width=2)

if sellAlert and showSignals
    // Calculate target and risk
    entryPrice = close
    slPrice = dn
    riskDistance = math.abs(entryPrice - slPrice)
    targetPrice = entryPrice - (riskDistance * 2)
    rrRatio = 2.0
    
    // Signal box
    box.new(bar_index, entryPrice + riskDistance*0.3, bar_index + 8, entryPrice - riskDistance*0.3, 
           bgcolor=color.new(color.red, 80), border_color=color.red, border_width=2)
    
    // Signal label
    label.new(bar_index, entryPrice, 
             "SELL SIGNAL\nEntry: " + str.tostring(entryPrice, "#.##") + 
             "\nTarget: " + str.tostring(targetPrice, "#.##") + 
             "\nSL: " + str.tostring(slPrice, "#.##") + 
             "\nR/R: " + str.tostring(rrRatio, "#.##"), 
             color=color.red, textcolor=color.white, style=label.style_label_center, size=size.small)
    
    // Target and SL lines
    line.new(bar_index, targetPrice, bar_index + 20, targetPrice, color=color.green, style=line.style_solid, width=2)
    line.new(bar_index, slPrice, bar_index + 20, slPrice, color=color.red, style=line.style_dashed, width=2)

// === ALERTS ===
alertcondition(buyAlert, title="Buy Signal", message="Buy: OB + Supertrend")
alertcondition(sellAlert, title="Sell Signal", message="Sell: OB + Supertrend")

// Fill between Supertrend lines
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=1)
fill(mPlot, upPlot, title="UpTrend Fill", color=color.new(color.green, 90))
fill(mPlot, dnPlot, title="DownTrend Fill", color=color.new(color.red, 90))
