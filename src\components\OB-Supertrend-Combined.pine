// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @version=5
indicator("Order Blocks + Supertrend Combined", overlay=true, max_boxes_count=500, max_labels_count=500, max_lines_count=500, max_bars_back=5000)

// === ORDER BLOCKS INPUTS ===
showInvalidated = input.bool(true, "Show Historic OB Zones", group="Order Blocks Configuration")
orderBlockVolumetricInfo = input.bool(true, "OB Volumetric Info", group="Order Blocks Configuration")
obEndMethod = input.string("Wick", "OB Zone Invalidation", options=["Wick", "Close"], group="Order Blocks Configuration")
combineOBs = input.bool(true, "Combine OB Zones", group="Order Blocks Configuration")
maxATRMult = input.float(3.5, "Max ATR Multiplier", group="Order Blocks Configuration")
swingLength = input.int(10, "Swing Length", minval=3, group="Order Blocks Configuration")
zoneCount = input.string("Low", "Zone Count", options=["High", "Medium", "Low", "One"], group="Order Blocks Configuration")
bullOrderBlockColor = input.color(#08998180, "Bullish OB Color", group="Order Blocks Style")
bearOrderBlockColor = input.color(#f2364680, "Bearish OB Color", group="Order Blocks Style")
textColor = input.color(#ffffff80, "Text Color", group="Order Blocks Style")
extendZonesBy = input.int(15, "Extend Zones", minval=1, maxval=30, group="Order Blocks Style")
extendZonesDynamic = input.bool(true, "Dynamic Zone Extension", group="Order Blocks Style")

// === SUPERTREND INPUTS ===
Periods = input.int(10, title="ATR Period", group="Supertrend Configuration")
src = input.source(hl2, title="Source", group="Supertrend Configuration")
Multiplier = input.float(3.0, title="ATR Multiplier", step=0.1, group="Supertrend Configuration")
changeATR = input.bool(true, title="Change ATR Calculation Method?", group="Supertrend Configuration")
showsignals = input.bool(true, title="Show Supertrend Signals?", group="Supertrend Configuration")
highlighting = input.bool(true, title="Supertrend Highlighter On/Off?", group="Supertrend Configuration")

// === COMBINED SIGNAL INPUTS ===
showCombinedSignals = input.bool(true, "Show Combined Entry Signals", group="Combined Signals")
showSLLines = input.bool(true, "Show Stop Loss Lines", group="Combined Signals")

// === ORDER BLOCKS CALCULATIONS ===
bullishOrderBlocksCount = zoneCount == "One" ? 1 : zoneCount == "Low" ? 3 : zoneCount == "Medium" ? 5 : 10
bearishOrderBlocksCount = zoneCount == "One" ? 1 : zoneCount == "Low" ? 3 : zoneCount == "Medium" ? 5 : 10
extendZonesByTime = extendZonesBy * timeframe.in_seconds(timeframe.period) * 1000
atr_ob = ta.atr(10)

// === SUPERTREND CALCULATIONS ===
atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = src - (Multiplier * atr)
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = src + (Multiplier * atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
var int trend = 1
trend := nz(trend[1], 1)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// === ORDER BLOCKS TYPES ===
type orderBlockInfo
    float top
    float bottom
    float obVolume
    string obType
    int startTime
    float bbVolume
    float obLowVolume
    float obHighVolume
    bool breaker
    int breakTime
    string timeframeStr
    bool disabled = false
    string combinedTimeframesStr = na
    bool combined = false

type orderBlock
    orderBlockInfo info
    bool isRendered = false
    box orderBox = na
    box orderBoxText = na
    box orderBoxPositive = na
    box orderBoxNegative = na
    line orderSeperator = na
    line orderTextSeperator = na

type obSwing
    int x = na
    float y = na
    float swingVolume = na
    bool crossed = false

// === ORDER BLOCKS ARRAYS ===
var bullishOrderBlocksList = array.new<orderBlockInfo>(0)
var bearishOrderBlocksList = array.new<orderBlockInfo>(0)
var allOrderBlocksList = array.new<orderBlock>(0)

// === ORDER BLOCKS FUNCTIONS ===
createOrderBlock(orderBlockInfo orderBlockInfoF) =>
    orderBlock newOrderBlock = orderBlock.new(orderBlockInfoF)
    newOrderBlock

safeDeleteOrderBlock(orderBlock orderBlockF) =>
    orderBlockF.isRendered := false
    box.delete(orderBlockF.orderBox)
    box.delete(orderBlockF.orderBoxText)
    box.delete(orderBlockF.orderBoxPositive)
    box.delete(orderBlockF.orderBoxNegative)
    line.delete(orderBlockF.orderSeperator)
    line.delete(orderBlockF.orderTextSeperator)

colorWithTransparency(colorF, transparencyX) =>
    color.new(colorF, color.t(colorF) * transparencyX)

createOBBox(boxColor, transparencyX = 1.0, xlocType = xloc.bar_time) =>
    box.new(na, na, na, na, text_size=size.normal, xloc=xlocType, extend=extend.none, bgcolor=colorWithTransparency(boxColor, transparencyX), text_color=textColor, text_halign=text.align_center, border_color=#00000000)

findOBSwings(len) =>
    var swingType = 0
    var obSwing top = obSwing.new(na, na)
    var obSwing bottom = obSwing.new(na, na)
    upper = ta.highest(len)
    lower = ta.lowest(len)
    swingType := high[len] > upper ? 0 : low[len] < lower ? 1 : swingType
    if swingType == 0 and swingType[1] != 0
        top := obSwing.new(bar_index[len], high[len], volume[len])
    if swingType == 1 and swingType[1] != 1
        bottom := obSwing.new(bar_index[len], low[len], volume[len])
    [top, bottom]

// Call findOBSwings on every bar for consistency
[globalTop, globalBtm] = findOBSwings(swingLength)

findOrderBlocks() =>
    if bar_index > last_bar_index - 1750
        top = globalTop
        btm = globalBtm
        useBody = false
        max = useBody ? math.max(close, open) : high
        min = useBody ? math.min(close, open) : low

        // Bullish Order Block
        if bullishOrderBlocksList.size() > 0
            for i = bullishOrderBlocksList.size() - 1 to 0
                currentOB = bullishOrderBlocksList.get(i)
                if not currentOB.breaker
                    if (obEndMethod == "Wick" ? low : math.min(open, close)) < currentOB.bottom
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if high > currentOB.top
                        bullishOrderBlocksList.remove(i)

        if close > top.y and not top.crossed
            top.crossed := true
            boxBtm = max[1]
            boxTop = min[1]
            boxLoc = time[1]
            for i = 1 to (bar_index - top.x) - 1
                boxBtm := math.min(min[i], boxBtm)
                boxTop := boxBtm == min[i] ? max[i] : boxTop
                boxLoc := boxBtm == min[i] ? time[i] : boxLoc
            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bull", boxLoc)
            newOrderBlockInfo.obLowVolume := volume[2]
            newOrderBlockInfo.obHighVolume := volume + volume[1]
            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr_ob * maxATRMult
                bullishOrderBlocksList.unshift(newOrderBlockInfo)
                if bullishOrderBlocksList.size() > 30
                    bullishOrderBlocksList.pop()

        // Bearish Order Block
        if bearishOrderBlocksList.size() > 0
            for i = bearishOrderBlocksList.size() - 1 to 0
                currentOB = bearishOrderBlocksList.get(i)
                if not currentOB.breaker
                    if (obEndMethod == "Wick" ? high : math.max(open, close)) > currentOB.top
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if low < currentOB.bottom
                        bearishOrderBlocksList.remove(i)

        if close < btm.y and not btm.crossed
            btm.crossed := true
            boxBtm = min[1]
            boxTop = max[1]
            boxLoc = time[1]
            for i = 1 to (bar_index - btm.x) - 1
                boxTop := math.max(max[i], boxTop)
                boxBtm := boxTop == max[i] ? min[i] : boxBtm
                boxLoc := boxTop == max[i] ? time[i] : boxLoc
            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bear", boxLoc)
            newOrderBlockInfo.obLowVolume := volume + volume[1]
            newOrderBlockInfo.obHighVolume := volume[2]
            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr_ob * maxATRMult
                bearishOrderBlocksList.unshift(newOrderBlockInfo)
                if bearishOrderBlocksList.size() > 30
                    bearishOrderBlocksList.pop()
    true

// === COMBINED SIGNAL LOGIC ===
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1

var bool inBullOB = false
var bool inBearOB = false
var float entryPrice = na
var float slPrice = na
var line slLine = na
var bool buyAlert = false
var bool sellAlert = false

// Check if price is in Order Block zones
if bullishOrderBlocksList.size() > 0
    inBullOB := false
    for i = 0 to math.min(bullishOrderBlocksList.size() - 1, bullishOrderBlocksCount - 1)
        ob = bullishOrderBlocksList.get(i)
        if not ob.breaker and close >= ob.bottom and close <= ob.top
            inBullOB := true
            break

if bearishOrderBlocksList.size() > 0
    inBearOB := false
    for i = 0 to math.min(bearishOrderBlocksList.size() - 1, bearishOrderBlocksCount - 1)
        ob = bearishOrderBlocksList.get(i)
        if not ob.breaker and close >= ob.bottom and close <= ob.top
            inBearOB := true
            break

// Combined signals: OB + Supertrend
buyAlert := buySignal and inBullOB
sellAlert := sellSignal and inBearOB

// Entry signals with SL
if buyAlert and showCombinedSignals
    entryPrice := close
    slPrice := up
    label.new(bar_index, close, "BUY ENTRY\n" + str.tostring(close, "#.##") + "\nSL: " + str.tostring(up, "#.##"), color=color.green, textcolor=color.white, style=label.style_label_down, size=size.normal)
    if showSLLines
        line.delete(slLine[1])
        slLine := line.new(bar_index, up, bar_index + 50, up, color=color.red, style=line.style_dashed, width=2)

if sellAlert and showCombinedSignals
    entryPrice := close
    slPrice := dn
    label.new(bar_index, close, "SELL ENTRY\n" + str.tostring(close, "#.##") + "\nSL: " + str.tostring(dn, "#.##"), color=color.red, textcolor=color.white, style=label.style_label_up, size=size.normal)
    if showSLLines
        line.delete(slLine[1])
        slLine := line.new(bar_index, dn, bar_index + 50, dn, color=color.green, style=line.style_dashed, width=2)

// === SUPERTREND VISUALIZATION ===
upPlot = plot(trend == 1 ? up : na, title="Up Trend", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot = plot(trend == -1 ? dn : na, title="Down Trend", style=plot.style_linebr, linewidth=2, color=color.red)
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=1)
longFillColor = highlighting ? (trend == 1 ? color.new(color.green, 80) : color.new(color.white, 100)) : color.new(color.white, 100)
shortFillColor = highlighting ? (trend == -1 ? color.new(color.red, 80) : color.new(color.white, 100)) : color.new(color.white, 100)
fill(mPlot, upPlot, title="UpTrend Highlighter", color=longFillColor)
fill(mPlot, dnPlot, title="DownTrend Highlighter", color=shortFillColor)

// === ALERTS ===
alertcondition(buyAlert, title="OB + Supertrend Buy", message="OB + Supertrend Buy Signal at " + str.tostring(close))
alertcondition(sellAlert, title="OB + Supertrend Sell", message="OB + Supertrend Sell Signal at " + str.tostring(close))

renderOrderBlock(orderBlock ob) =>
    orderBlockInfo info = ob.info
    ob.isRendered := true
    orderColor = info.obType == "Bull" ? bullOrderBlockColor : bearOrderBlockColor
    if not info.breaker or showInvalidated
        ob.orderBox := createOBBox(orderColor, 1.5)
        if info.combined
            ob.orderBox.set_bgcolor(colorWithTransparency(orderColor, 1.1))
        ob.orderBoxText := createOBBox(color.new(color.white, 100))

        zoneSize = extendZonesDynamic ? na(info.breakTime) ? extendZonesByTime : (info.breakTime - info.startTime) : extendZonesByTime
        if na(info.breakTime)
            zoneSize := (time + 1) - info.startTime

        box.set_lefttop(ob.orderBox, info.startTime, info.top)
        box.set_rightbottom(ob.orderBox, info.startTime + zoneSize, info.bottom)
        box.set_lefttop(ob.orderBoxText, info.startTime, info.top)
        box.set_rightbottom(ob.orderBoxText, info.startTime + zoneSize/3, info.bottom)

        OBText = info.timeframeStr + " OB"
        box.set_text(ob.orderBoxText, (orderBlockVolumetricInfo ? str.tostring(info.obVolume, format.volume) + "\n" : "") + OBText)

handleOrderBlocksFinal() =>
    if allOrderBlocksList.size() > 0
        for i = 0 to allOrderBlocksList.size() - 1
            safeDeleteOrderBlock(allOrderBlocksList.get(i))
    allOrderBlocksList.clear()

    for j = 0 to math.min(bullishOrderBlocksList.size() - 1, bullishOrderBlocksCount - 1)
        orderBlockInfoF = bullishOrderBlocksList.get(j)
        orderBlockInfoF.timeframeStr := timeframe.period
        allOrderBlocksList.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))

    for j = 0 to math.min(bearishOrderBlocksList.size() - 1, bearishOrderBlocksCount - 1)
        orderBlockInfoF = bearishOrderBlocksList.get(j)
        orderBlockInfoF.timeframeStr := timeframe.period
        allOrderBlocksList.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))

    if allOrderBlocksList.size() > 0
        for i = 0 to allOrderBlocksList.size() - 1
            curOB = allOrderBlocksList.get(i)
            if not curOB.info.disabled
                renderOrderBlock(curOB)

// === EXECUTE ORDER BLOCKS LOGIC ===
findOrderBlocks()
if barstate.isconfirmed
    handleOrderBlocksFinal()
