// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @version=5
indicator("OB + Supertrend Combined", overlay=true, max_boxes_count=20, max_labels_count=20, max_lines_count=20)

// === INPUTS ===
Periods = input.int(10, title="ATR Period")
Multiplier = input.float(3.0, title="ATR Multiplier", step=0.1)
showSignals = input.bool(true, "Show Signals")
showOB = input.bool(true, "Show Order Blocks")
swingLength = input.int(10, "Swing Length", minval=3)
maxATRMult = input.float(3.5, "Max ATR Multiplier")
obEndMethod = input.string("Wick", "OB Zone Invalidation", options=["Wick", "Close"])
maxDistanceToLastBar = input.int(1750, "Max Distance To Last Bar")
maxOrderBlocks = input.int(30, "Max Order Blocks")
bullishOrderBlocks = input.int(3, "Bullish Order Blocks Count")
bearishOrderBlocks = input.int(3, "Bearish Order Blocks Count")

// === SUPERTREND CALCULATION ===
atr = ta.atr(Periods)
up = hl2 - (Multiplier * atr)
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = hl2 + (Multiplier * atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn

var int trend = 1
trend := nz(trend[1], 1)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// Supertrend signals
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1

// === ORDER BLOCKS TYPES ===
type orderBlockInfo
    float top
    float bottom
    float obVolume
    string obType
    int startTime
    float bbVolume
    float obLowVolume
    float obHighVolume
    bool breaker
    int breakTime
    string timeframeStr
    bool disabled = false
    string combinedTimeframesStr = na
    bool combined = false

type obSwing
    int x = na
    float y = na
    float swingVolume = na
    bool crossed = false

// === ORDER BLOCKS ARRAYS ===
var bullishOrderBlocksList = array.new<orderBlockInfo>(0)
var bearishOrderBlocksList = array.new<orderBlockInfo>(0)

// === ORDER BLOCKS FUNCTIONS ===
findOBSwings(len) =>
    var swingType = 0
    var obSwing top = obSwing.new(na, na)
    var obSwing bottom = obSwing.new(na, na)

    upper = ta.highest(len)
    lower = ta.lowest(len)

    swingType := high[len] > upper ? 0 : low[len] < lower ? 1 : swingType

    if swingType == 0 and swingType[1] != 0
        top := obSwing.new(bar_index[len], high[len], volume[len])

    if swingType == 1 and swingType[1] != 1
        bottom := obSwing.new(bar_index[len], low[len], volume[len])

    [top, bottom]

// Call findOBSwings on every bar for consistency
[globalTop, globalBtm] = findOBSwings(swingLength)

findOrderBlocks() =>
    if bar_index > last_bar_index - maxDistanceToLastBar
        top = globalTop
        btm = globalBtm
        useBody = false
        max = useBody ? math.max(close, open) : high
        min = useBody ? math.min(close, open) : low

        // Bullish Order Block
        bullishBreaked = 0

        if bullishOrderBlocksList.size() > 0
            for i = bullishOrderBlocksList.size() - 1 to 0
                currentOB = bullishOrderBlocksList.get(i)

                if not currentOB.breaker
                    if (obEndMethod == "Wick" ? low : math.min(open, close)) < currentOB.bottom
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if high > currentOB.top
                        bullishOrderBlocksList.remove(i)
                    else if i < bullishOrderBlocks and top.y < currentOB.top and top.y > currentOB.bottom
                        bullishBreaked := 1

        if close > top.y and not top.crossed
            top.crossed := true

            boxBtm = max[1]
            boxTop = min[1]
            boxLoc = time[1]

            for i = 1 to (bar_index - top.x) - 1
                boxBtm := math.min(min[i], boxBtm)
                boxTop := boxBtm == min[i] ? max[i] : boxTop
                boxLoc := boxBtm == min[i] ? time[i] : boxLoc

            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bull", boxLoc)
            newOrderBlockInfo.obLowVolume := volume[2]
            newOrderBlockInfo.obHighVolume := volume + volume[1]

            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr * maxATRMult
                bullishOrderBlocksList.unshift(newOrderBlockInfo)
                if bullishOrderBlocksList.size() > maxOrderBlocks
                    bullishOrderBlocksList.pop()

        // Bearish Order Block
        bearishBreaked = 0

        if bearishOrderBlocksList.size() > 0
            for i = bearishOrderBlocksList.size() - 1 to 0
                currentOB = bearishOrderBlocksList.get(i)

                if not currentOB.breaker
                    if (obEndMethod == "Wick" ? high : math.max(open, close)) > currentOB.top
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if low < currentOB.bottom
                        bearishOrderBlocksList.remove(i)
                    else if i < bearishOrderBlocks and btm.y > currentOB.bottom and btm.y < currentOB.top
                        bearishBreaked := 1

        if close < btm.y and not btm.crossed
            btm.crossed := true

            boxBtm = min[1]
            boxTop = max[1]
            boxLoc = time[1]

            for i = 1 to (bar_index - btm.x) - 1
                boxTop := math.max(max[i], boxTop)
                boxBtm := boxTop == max[i] ? min[i] : boxBtm
                boxLoc := boxTop == max[i] ? time[i] : boxLoc

            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bear", boxLoc)
            newOrderBlockInfo.obLowVolume := volume + volume[1]
            newOrderBlockInfo.obHighVolume := volume[2]

            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr * maxATRMult
                bearishOrderBlocksList.unshift(newOrderBlockInfo)
                if bearishOrderBlocksList.size() > maxOrderBlocks
                    bearishOrderBlocksList.pop()
    true

// Check if in OB zones
var bool inBullOB = false
var bool inBearOB = false

if bullishOrderBlocksList.size() > 0
    inBullOB := false
    for i = 0 to math.min(bullishOrderBlocksList.size() - 1, bullishOrderBlocks - 1)
        ob = bullishOrderBlocksList.get(i)
        if not ob.breaker and close >= ob.bottom and close <= ob.top
            inBullOB := true
            break

if bearishOrderBlocksList.size() > 0
    inBearOB := false
    for i = 0 to math.min(bearishOrderBlocksList.size() - 1, bearishOrderBlocks - 1)
        ob = bearishOrderBlocksList.get(i)
        if not ob.breaker and close >= ob.bottom and close <= ob.top
            inBearOB := true
            break

// === COMBINED SIGNALS ===
buyAlert = buySignal and inBullOB
sellAlert = sellSignal and inBearOB

// === ORDER BLOCKS VISUALIZATION ===
renderOrderBlocks() =>
    if showOB
        // Render Bullish Order Blocks
        if bullishOrderBlocksList.size() > 0
            for i = 0 to math.min(bullishOrderBlocksList.size() - 1, bullishOrderBlocks - 1)
                ob = bullishOrderBlocksList.get(i)
                if not ob.breaker
                    zoneSize = 50 * timeframe.in_seconds(timeframe.period) * 1000
                    box.new(ob.startTime, ob.top, ob.startTime + zoneSize, ob.bottom,
                           bgcolor=color.new(color.green, 85), border_color=color.green)

        // Render Bearish Order Blocks
        if bearishOrderBlocksList.size() > 0
            for i = 0 to math.min(bearishOrderBlocksList.size() - 1, bearishOrderBlocks - 1)
                ob = bearishOrderBlocksList.get(i)
                if not ob.breaker
                    zoneSize = 50 * timeframe.in_seconds(timeframe.period) * 1000
                    box.new(ob.startTime, ob.top, ob.startTime + zoneSize, ob.bottom,
                           bgcolor=color.new(color.red, 85), border_color=color.red)

// === VISUALIZATION ===
// Supertrend lines
upPlot = plot(trend == 1 ? up : na, title="Up Trend", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot = plot(trend == -1 ? dn : na, title="Down Trend", style=plot.style_linebr, linewidth=2, color=color.red)

// === SIGNAL VISUALIZATION ===
// Debug shapes - always show Supertrend signals
plotshape(buySignal, title="ST Buy", location=location.belowbar, style=shape.triangleup, size=size.tiny, color=color.blue)
plotshape(sellSignal, title="ST Sell", location=location.abovebar, style=shape.triangledown, size=size.tiny, color=color.orange)

// Background when in OB
bgcolor(inBullOB ? color.new(color.green, 95) : na, title="In Bull OB")
bgcolor(inBearOB ? color.new(color.red, 95) : na, title="In Bear OB")

// Combined signals
if buyAlert and showSignals
    // Calculate target and risk
    entryPrice = close
    slPrice = up
    riskDistance = math.abs(entryPrice - slPrice)
    targetPrice = entryPrice + (riskDistance * 2)
    rrRatio = 2.0
    
    // Signal box
    box.new(bar_index, entryPrice + riskDistance*0.3, bar_index + 8, entryPrice - riskDistance*0.3, 
           bgcolor=color.new(color.green, 80), border_color=color.green, border_width=2)
    
    // Signal label
    label.new(bar_index, entryPrice, 
             "BUY SIGNAL\nEntry: " + str.tostring(entryPrice, "#.##") + 
             "\nTarget: " + str.tostring(targetPrice, "#.##") + 
             "\nSL: " + str.tostring(slPrice, "#.##") + 
             "\nR/R: " + str.tostring(rrRatio, "#.##"), 
             color=color.green, textcolor=color.white, style=label.style_label_center, size=size.small)
    
    // Target and SL lines
    line.new(bar_index, targetPrice, bar_index + 20, targetPrice, color=color.green, style=line.style_solid, width=2)
    line.new(bar_index, slPrice, bar_index + 20, slPrice, color=color.red, style=line.style_dashed, width=2)

if sellAlert and showSignals
    // Calculate target and risk
    entryPrice = close
    slPrice = dn
    riskDistance = math.abs(entryPrice - slPrice)
    targetPrice = entryPrice - (riskDistance * 2)
    rrRatio = 2.0
    
    // Signal box
    box.new(bar_index, entryPrice + riskDistance*0.3, bar_index + 8, entryPrice - riskDistance*0.3, 
           bgcolor=color.new(color.red, 80), border_color=color.red, border_width=2)
    
    // Signal label
    label.new(bar_index, entryPrice, 
             "SELL SIGNAL\nEntry: " + str.tostring(entryPrice, "#.##") + 
             "\nTarget: " + str.tostring(targetPrice, "#.##") + 
             "\nSL: " + str.tostring(slPrice, "#.##") + 
             "\nR/R: " + str.tostring(rrRatio, "#.##"), 
             color=color.red, textcolor=color.white, style=label.style_label_center, size=size.small)
    
    // Target and SL lines
    line.new(bar_index, targetPrice, bar_index + 20, targetPrice, color=color.green, style=line.style_solid, width=2)
    line.new(bar_index, slPrice, bar_index + 20, slPrice, color=color.red, style=line.style_dashed, width=2)

// === ALERTS ===
alertcondition(buyAlert, title="Buy Signal", message="Buy: OB + Supertrend")
alertcondition(sellAlert, title="Sell Signal", message="Sell: OB + Supertrend")

// === EXECUTE ORDER BLOCKS LOGIC ===
findOrderBlocks()

// Fill between Supertrend lines
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=1)
fill(mPlot, upPlot, title="UpTrend Fill", color=color.new(color.green, 90))
fill(mPlot, dnPlot, title="DownTrend Fill", color=color.new(color.red, 90))
