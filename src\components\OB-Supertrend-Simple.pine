// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @version=5
indicator("Order Blocks + Supertrend Simple", overlay=true, max_boxes_count=50, max_labels_count=50, max_lines_count=50)

// === SUPERTREND INPUTS ===
Periods = input.int(10, title="ATR Period", group="Supertrend")
src = input.source(hl2, title="Source", group="Supertrend") 
Multiplier = input.float(3.0, title="ATR Multiplier", step=0.1, group="Supertrend")
changeATR = input.bool(true, title="Change ATR Calculation Method?", group="Supertrend")
highlighting = input.bool(true, title="Supertrend Highlighter On/Off?", group="Supertrend")

// === ORDER BLOCKS INPUTS ===
swingLength = input.int(10, "Swing Length", minval=3, group="Order Blocks")
maxATRMult = input.float(3.5, "Max ATR Multiplier", group="Order Blocks")
bullOrderBlockColor = input.color(color.new(color.green, 80), "Bullish OB Color", group="Order Blocks")
bearOrderBlockColor = input.color(color.new(color.red, 80), "Bearish OB Color", group="Order Blocks")

// === SIGNAL INPUTS ===
showSignals = input.bool(true, "Show Combined Signals", group="Signals")
showSLLines = input.bool(true, "Show Stop Loss Lines", group="Signals")

// === SUPERTREND CALCULATIONS ===
atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = src - (Multiplier * atr)
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = src + (Multiplier * atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
var int trend = 1
trend := nz(trend[1], 1)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// === SIMPLE ORDER BLOCKS DETECTION ===
atr_ob = ta.atr(10)

// Detect swing highs and lows
swingHigh = ta.pivothigh(high, swingLength, swingLength)
swingLow = ta.pivotlow(low, swingLength, swingLength)

// Order Block variables
var float bullOB_top = na
var float bullOB_bottom = na
var float bearOB_top = na
var float bearOB_bottom = na
var int bullOB_time = na
var int bearOB_time = na
var bool bullOB_active = false
var bool bearOB_active = false

// Create bullish order block when price breaks above swing high
if not na(swingHigh) and close > swingHigh
    // Find the lowest low before the break
    bullOB_bottom := low[swingLength]
    bullOB_top := high[swingLength]
    for i = 1 to swingLength * 2
        if i < bar_index
            if low[i] < bullOB_bottom
                bullOB_bottom := low[i]
                bullOB_top := high[i]
    
    obSize = math.abs(bullOB_top - bullOB_bottom)
    if obSize <= atr_ob * maxATRMult
        bullOB_time := time[swingLength]
        bullOB_active := true

// Create bearish order block when price breaks below swing low
if not na(swingLow) and close < swingLow
    // Find the highest high before the break
    bearOB_top := high[swingLength]
    bearOB_bottom := low[swingLength]
    for i = 1 to swingLength * 2
        if i < bar_index
            if high[i] > bearOB_top
                bearOB_top := high[i]
                bearOB_bottom := low[i]
    
    obSize = math.abs(bearOB_top - bearOB_bottom)
    if obSize <= atr_ob * maxATRMult
        bearOB_time := time[swingLength]
        bearOB_active := true

// Invalidate order blocks
if bullOB_active and low < bullOB_bottom
    bullOB_active := false

if bearOB_active and high > bearOB_top
    bearOB_active := false

// === COMBINED SIGNAL LOGIC ===
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1

// Check if price is in Order Block zones
inBullOB = bullOB_active and close >= bullOB_bottom and close <= bullOB_top
inBearOB = bearOB_active and close >= bearOB_bottom and close <= bearOB_top

// Combined signals: OB + Supertrend
buyAlert = buySignal and inBullOB
sellAlert = sellSignal and inBearOB

// === VISUALIZATION ===
// Draw Order Blocks
var box bullOB_box = na
var box bearOB_box = na

if bullOB_active and na(bullOB_box)
    bullOB_box := box.new(bullOB_time, bullOB_top, time + 50*timeframe.in_seconds(timeframe.period)*1000, bullOB_bottom, 
                         bgcolor=bullOrderBlockColor, border_color=color.new(color.green, 50))

if bearOB_active and na(bearOB_box)
    bearOB_box := box.new(bearOB_time, bearOB_top, time + 50*timeframe.in_seconds(timeframe.period)*1000, bearOB_bottom, 
                         bgcolor=bearOrderBlockColor, border_color=color.new(color.red, 50))

if not bullOB_active and not na(bullOB_box)
    box.delete(bullOB_box)
    bullOB_box := na

if not bearOB_active and not na(bearOB_box)
    box.delete(bearOB_box)
    bearOB_box := na

// === SIGNAL VISUALIZATION ===
plotshape(buyAlert and showSignals ? low : na, title="Buy Signal", location=location.belowbar, style=shape.arrowup, size=size.normal, color=color.green)
plotshape(sellAlert and showSignals ? high : na, title="Sell Signal", location=location.abovebar, style=shape.arrowdown, size=size.normal, color=color.red)

// Entry labels
if buyAlert and showSignals
    label.new(bar_index, low, "BUY\n" + str.tostring(close, "#.##") + "\nSL: " + str.tostring(up, "#.##"), 
             color=color.green, textcolor=color.white, style=label.style_label_up, size=size.small)

if sellAlert and showSignals
    label.new(bar_index, high, "SELL\n" + str.tostring(close, "#.##") + "\nSL: " + str.tostring(dn, "#.##"), 
             color=color.red, textcolor=color.white, style=label.style_label_down, size=size.small)

// Stop Loss Lines
var line slLine = na
if buyAlert and showSLLines
    line.delete(slLine)
    slLine := line.new(bar_index, up, bar_index + 20, up, color=color.red, style=line.style_dashed, width=2)

if sellAlert and showSLLines
    line.delete(slLine)
    slLine := line.new(bar_index, dn, bar_index + 20, dn, color=color.green, style=line.style_dashed, width=2)

// === SUPERTREND VISUALIZATION ===
upPlot = plot(trend == 1 ? up : na, title="Up Trend", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot = plot(trend == -1 ? dn : na, title="Down Trend", style=plot.style_linebr, linewidth=2, color=color.red)
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=1)

longFillColor = highlighting ? (trend == 1 ? color.new(color.green, 90) : color.new(color.white, 100)) : color.new(color.white, 100)
shortFillColor = highlighting ? (trend == -1 ? color.new(color.red, 90) : color.new(color.white, 100)) : color.new(color.white, 100)

fill(mPlot, upPlot, title="UpTrend Highlighter", color=longFillColor)
fill(mPlot, dnPlot, title="DownTrend Highlighter", color=shortFillColor)

// === ALERTS ===
alertcondition(buyAlert, title="OB + Supertrend Buy", message="Buy Signal: OB + Supertrend")
alertcondition(sellAlert, title="OB + Supertrend Sell", message="Sell Signal: OB + Supertrend")
