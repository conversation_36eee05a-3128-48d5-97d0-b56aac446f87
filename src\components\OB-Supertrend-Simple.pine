// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @version=5
indicator("Order Blocks + Supertrend Simple", overlay=true, max_boxes_count=50, max_labels_count=50, max_lines_count=50)

// === SUPERTREND INPUTS ===
Periods = input.int(10, title="ATR Period", group="Supertrend")
src = input.source(hl2, title="Source", group="Supertrend")
Multiplier = input.float(3.0, title="ATR Multiplier", step=0.1, group="Supertrend")
changeATR = input.bool(true, title="Change ATR Calculation Method?", group="Supertrend")
highlighting = input.bool(true, title="Supertrend Highlighter On/Off?", group="Supertrend")

// === ORDER BLOCKS INPUTS ===
swingLength = input.int(5, "Swing Length", minval=3, group="Order Blocks")
maxATRMult = input.float(5.0, "Max ATR Multiplier", group="Order Blocks")
bullOrderBlockColor = input.color(color.new(color.green, 80), "Bullish OB Color", group="Order Blocks")
bearOrderBlockColor = input.color(color.new(color.red, 80), "Bearish OB Color", group="Order Blocks")

// === SIGNAL INPUTS ===
showSignals = input.bool(true, "Show Combined Signals", group="Signals")
showSLLines = input.bool(true, "Show Stop Loss Lines", group="Signals")
showOBZones = input.bool(true, "Show Order Block Zones", group="Signals")

// === SUPERTREND CALCULATIONS ===
atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = src - (Multiplier * atr)
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = src + (Multiplier * atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
var int trend = 1
trend := nz(trend[1], 1)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// === SIMPLE ORDER BLOCKS DETECTION ===
atr_ob = ta.atr(10)

// Simplified Order Block detection using recent highs/lows
var float bullOB_top = na
var float bullOB_bottom = na
var float bearOB_top = na
var float bearOB_bottom = na
var int bullOB_time = na
var int bearOB_time = na
var bool bullOB_active = false
var bool bearOB_active = false

// Create bullish order block when we have a strong upward move
if close > close[5] and high > ta.highest(high, 20)[1]
    bullOB_bottom := ta.lowest(low, 10)[1]
    bullOB_top := ta.highest(high, 10)[1]
    bullOB_time := time
    bullOB_active := true

// Create bearish order block when we have a strong downward move
if close < close[5] and low < ta.lowest(low, 20)[1]
    bearOB_top := ta.highest(high, 10)[1]
    bearOB_bottom := ta.lowest(low, 10)[1]
    bearOB_time := time
    bearOB_active := true

// Invalidate order blocks when price breaks through
if bullOB_active and close < bullOB_bottom
    bullOB_active := false

if bearOB_active and close > bearOB_top
    bearOB_active := false

// Keep OB active for limited time (50 bars)
if bullOB_active and bar_index - bullOB_time/timeframe.in_seconds(timeframe.period)/1000 > 50
    bullOB_active := false

if bearOB_active and bar_index - bearOB_time/timeframe.in_seconds(timeframe.period)/1000 > 50
    bearOB_active := false

// === COMBINED SIGNAL LOGIC ===
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1

// Check if price is in Order Block zones
inBullOB = bullOB_active and close >= bullOB_bottom and close <= bullOB_top
inBearOB = bearOB_active and close >= bearOB_bottom and close <= bearOB_top

// Combined signals: OB + Supertrend
buyAlert = buySignal and inBullOB
sellAlert = sellSignal and inBearOB

// === VISUALIZATION ===
// Draw Order Blocks
var box bullOB_box = na
var box bearOB_box = na

if bullOB_active and na(bullOB_box)
    bullOB_box := box.new(bullOB_time, bullOB_top, time + 50*timeframe.in_seconds(timeframe.period)*1000, bullOB_bottom, 
                         bgcolor=bullOrderBlockColor, border_color=color.new(color.green, 50))

if bearOB_active and na(bearOB_box)
    bearOB_box := box.new(bearOB_time, bearOB_top, time + 50*timeframe.in_seconds(timeframe.period)*1000, bearOB_bottom, 
                         bgcolor=bearOrderBlockColor, border_color=color.new(color.red, 50))

if not bullOB_active and not na(bullOB_box)
    box.delete(bullOB_box)
    bullOB_box := na

if not bearOB_active and not na(bearOB_box)
    box.delete(bearOB_box)
    bearOB_box := na

// === SIGNAL VISUALIZATION ===
// Calculate target and risk/reward
var float buyTarget = na
var float sellTarget = na
var float buyEntry = na
var float sellEntry = na
var float buySL = na
var float sellSL = na

if buyAlert and showSignals
    buyEntry := close
    buySL := up
    // Target is 2x the risk distance
    riskDistance = math.abs(buyEntry - buySL)
    buyTarget := buyEntry + (riskDistance * 2)

    // Calculate amounts and ratios
    entryPrice = buyEntry
    targetPrice = buyTarget
    slPrice = buySL
    riskAmount = math.abs(entryPrice - slPrice)
    rewardAmount = math.abs(targetPrice - entryPrice)
    rrRatio = rewardAmount / riskAmount

    // Create signal box
    signalBox = box.new(bar_index, buyEntry + riskDistance * 0.5, bar_index + 10, buyEntry - riskDistance * 0.5,
                       bgcolor=color.new(color.green, 85), border_color=color.green, border_width=2)

    // Add signal label with detailed info
    label.new(bar_index + 5, buyEntry,
             "Target: " + str.tostring(buyTarget, "#.##") + " (" + str.tostring((rewardAmount/entryPrice)*100, "#.##") + "%) " + str.tostring(buyTarget, "#.##") + ", Amount: " + str.tostring(rewardAmount, "#.##") + "\n" +
             "Open PnL: " + str.tostring(rewardAmount, "#.##") + ", Chg: " + str.tostring((rewardAmount/entryPrice)*100, "#.##") + "\n" +
             "Risk/Reward Ratio: " + str.tostring(rrRatio, "#.##"),
             color=color.new(color.green, 0), textcolor=color.white, style=label.style_label_center, size=size.small)

if sellAlert and showSignals
    sellEntry := close
    sellSL := dn
    // Target is 2x the risk distance
    riskDistance = math.abs(sellEntry - sellSL)
    sellTarget := sellEntry - (riskDistance * 2)

    // Calculate amounts and ratios
    entryPrice = sellEntry
    targetPrice = sellTarget
    slPrice = sellSL
    riskAmount = math.abs(entryPrice - slPrice)
    rewardAmount = math.abs(entryPrice - targetPrice)
    rrRatio = rewardAmount / riskAmount

    // Create signal box
    signalBox = box.new(bar_index, sellEntry + riskDistance * 0.5, bar_index + 10, sellEntry - riskDistance * 0.5,
                       bgcolor=color.new(color.red, 85), border_color=color.red, border_width=2)

    // Add signal label with detailed info
    label.new(bar_index + 5, sellEntry,
             "Target: " + str.tostring(sellTarget, "#.##") + " (" + str.tostring((rewardAmount/entryPrice)*100, "#.##") + "%) " + str.tostring(sellTarget, "#.##") + ", Amount: " + str.tostring(rewardAmount, "#.##") + "\n" +
             "Open PnL: " + str.tostring(rewardAmount, "#.##") + ", Chg: " + str.tostring((rewardAmount/entryPrice)*100, "#.##") + "\n" +
             "Risk/Reward Ratio: " + str.tostring(rrRatio, "#.##"),
             color=color.new(color.red, 0), textcolor=color.white, style=label.style_label_center, size=size.small)

// Target and Stop Loss Lines
var line slLine = na
var line tpLine = na

if buyAlert and showSLLines
    line.delete(slLine)
    line.delete(tpLine)
    // Stop Loss line (red)
    slLine := line.new(bar_index, buySL, bar_index + 30, buySL, color=color.red, style=line.style_dashed, width=2)
    // Target line (green)
    tpLine := line.new(bar_index, buyTarget, bar_index + 30, buyTarget, color=color.green, style=line.style_solid, width=2)

if sellAlert and showSLLines
    line.delete(slLine)
    line.delete(tpLine)
    // Stop Loss line (red)
    slLine := line.new(bar_index, sellSL, bar_index + 30, sellSL, color=color.red, style=line.style_dashed, width=2)
    // Target line (green)
    tpLine := line.new(bar_index, sellTarget, bar_index + 30, sellTarget, color=color.green, style=line.style_solid, width=2)

// === SUPERTREND VISUALIZATION ===
upPlot = plot(trend == 1 ? up : na, title="Up Trend", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot = plot(trend == -1 ? dn : na, title="Down Trend", style=plot.style_linebr, linewidth=2, color=color.red)
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=1)

longFillColor = highlighting ? (trend == 1 ? color.new(color.green, 90) : color.new(color.white, 100)) : color.new(color.white, 100)
shortFillColor = highlighting ? (trend == -1 ? color.new(color.red, 90) : color.new(color.white, 100)) : color.new(color.white, 100)

fill(mPlot, upPlot, title="UpTrend Highlighter", color=longFillColor)
fill(mPlot, dnPlot, title="DownTrend Highlighter", color=shortFillColor)

// === ALERTS ===
alertcondition(buyAlert, title="OB + Supertrend Buy", message="Buy Signal: OB + Supertrend")
alertcondition(sellAlert, title="OB + Supertrend Sell", message="Sell Signal: OB + Supertrend")
