'use client';

interface PermissionButtonProps {
  onRequestPermission: () => Promise<boolean>;
  hasPermission: boolean;
  isSupported: boolean;
}

export const PermissionButton = ({ 
  onRequestPermission, 
  hasPermission, 
  isSupported 
}: PermissionButtonProps) => {
  if (!isSupported) {
    return (
      <div className="text-center p-4 bg-gray-800 rounded-lg">
        <p className="text-gray-400">
          Shake detection is not supported on this device
        </p>
      </div>
    );
  }

  if (hasPermission) {
    return (
      <div className="text-center p-4 bg-green-900/30 border border-green-600 rounded-lg">
        <p className="text-green-400 font-medium">
          ✅ Shake detection enabled!
        </p>
        <p className="text-sm text-gray-400 mt-1">
          Shake your device to randomize the robot's emotion
        </p>
      </div>
    );
  }

  return (
    <div className="text-center p-4 bg-gray-800 rounded-lg">
      <p className="text-gray-300 mb-3">
        Enable shake detection to change emotions by shaking your device
      </p>
      <button
        onClick={onRequestPermission}
        className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors"
      >
        Enable Shake Detection
      </button>
    </div>
  );
};