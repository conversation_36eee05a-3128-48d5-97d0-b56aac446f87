"use client";

import { EmotionType } from "@/utils/emotionTypes";
import { getEmotionIntensity } from "@/lib/emotionLogic";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface RobotFaceProps {
  emotion: EmotionType;
  className?: string;
  svgRef?: React.RefObject<SVGSVGElement | null>;
}

export const RobotFace = ({
  emotion,
  className = "",
  svgRef,
}: RobotFaceProps) => {
  const [isBlinking, setIsBlinking] = useState(false);
  const [eyeParticles, setEyeParticles] = useState<
    Array<{ id: number; x: number; y: number }>
  >([]);
  const intensity = getEmotionIntensity(emotion);

  // Auto-blink animation with more natural timing
  useEffect(() => {
    if (emotion.includes("blink")) return;

    const blinkInterval = setInterval(() => {
      setIsBlinking(true);
      setTimeout(() => setIsBlinking(false), 120);
    }, 2500 + Math.random() * 3000);

    return () => clearInterval(blinkInterval);
  }, [emotion]);

  // Particle effects for certain emotions
  useEffect(() => {
    if (emotion === "glee" || emotion === "awe") {
      const particles = Array.from({ length: 6 }, (_, i) => ({
        id: i,
        x: 120 + (Math.random() - 0.5) * 100,
        y: 100 + (Math.random() - 0.5) * 60,
      }));
      setEyeParticles(particles);

      const timer = setTimeout(() => setEyeParticles([]), 2000);
      return () => clearTimeout(timer);
    } else {
      setEyeParticles([]);
    }
  }, [emotion]);

  interface EyeShape {
    height: number;
    width: number;
    curve: number;
    tilt?: number;
    pupilSize?: number;
    lidTop?: number;
    lidBottom?: number;
    shape?: string;
  }

  const getEyeShape = (): EyeShape => {
    if (isBlinking || emotion === "blink_high")
      return {
        height: 2,
        width: 14,
        curve: 1,
        lidTop: 5,
        lidBottom: 5,
        shape: "ellipse",
      };
    if (emotion === "blink_low")
      return { height: 6, width: 15, curve: 2, shape: "ellipse" };

    switch (emotion) {
      case "sleepy":
        return {
          height: 5,
          width: 14,
          curve: 2,
          lidTop: 3,
          pupilSize: 2,
          shape: "ellipse",
        };
      case "sad_down":
        return {
          height: 6,
          width: 12,
          curve: 4,
          lidTop: 4,
          tilt: -5,
          shape: "ellipse",
        };
      case "sad_up":
        return {
          height: 6,
          width: 12,
          curve: 4,
          lidBottom: 4,
          tilt: 5,
          shape: "ellipse",
        };
      case "angry":
        return {
          height: 7,
          width: 10,
          curve: 2,
          lidTop: 2,
          tilt: -10,
          shape: "diamond",
        };
      case "furious":
        return {
          height: 6,
          width: 9,
          curve: 5,
          lidTop: 1,
          pupilSize: 1,
          tilt: -15,
          shape: "diamond",
        };
      case "annoyed":
        return {
          height: 7,
          width: 11,
          curve: 3,
          lidTop: 3,
          tilt: -7,
          shape: "ellipse",
        };
      case "squint":
        return {
          height: 3,
          width: 14,
          curve: 1,
          lidTop: 4,
          lidBottom: 4,
          shape: "line",
        };
      case "suspicious":
        return {
          height: 7,
          width: 12,
          curve: 2,
          tilt: 10,
          pupilSize: 2,
          shape: "ellipse",
        };
      case "skeptic":
        return {
          height: 6,
          width: 13,
          curve: 3,
          lidTop: 1,
          tilt: -5,
          shape: "ellipse",
        };
      case "unimpressed":
        return {
          height: 5,
          width: 12,
          curve: 1,
          lidTop: 4,
          pupilSize: 3,
          shape: "ellipse",
        };
      case "surprised":
        return {
          height: 10,
          width: 14,
          curve: 6,
          pupilSize: 5,
          shape: "circle",
        };
      case "awe":
        return {
          height: 9,
          width: 13,
          curve: 5,
          pupilSize: 4,
          shape: "circle",
        };
      case "scared":
        return {
          height: 9,
          width: 14,
          curve: 3,
          lidTop: 1,
          pupilSize: 5,
          shape: "circle",
        };
      case "worried":
        return {
          height: 7,
          width: 12,
          curve: 4,
          lidTop: 3,
          tilt: 7,
          shape: "ellipse",
        };
      case "focused":
        return {
          height: 7,
          width: 14,
          curve: 2,
          pupilSize: 4,
          shape: "rectangle",
        };
      case "frustrated":
        return {
          height: 6,
          width: 11,
          curve: 4,
          lidTop: 2,
          tilt: -6,
          shape: "ellipse",
        };
      case "happy":
        return {
          height: 8,
          width: 13,
          curve: 4,
          lidBottom: 1,
          pupilSize: 3,
          shape: "happy",
        };
      case "glee":
        return {
          height: 9,
          width: 13,
          curve: 5,
          lidBottom: 1,
          pupilSize: 4,
          shape: "star",
        };
      case "joy":
        return {
          height: 8,
          width: 12,
          curve: 5,
          lidBottom: 2,
          shape: "ellipse",
        };
      case "neutral":
        return { height: 7, width: 13, curve: 2, shape: "ellipse" };
      // case "blink":
      //   return {
      //     height: 2,
      //     width: 14,
      //     curve: 1,
      //     lidTop: 5,
      //     lidBottom: 5,
      //     shape: "ellipse",
      //   };
      default:
        return { height: 7, width: 13, curve: 2, shape: "ellipse" }; // neutral
    }
  };

  const getEyeColor = () => {
    const colors = {
      happy: "#00ff88",
      glee: "#00ffcc",
      sad_down: "#4488ff",
      sad_up: "#6699ff",
      angry: "#ff4444",
      furious: "#ff0000",
      surprised: "#ffaa00",
      awe: "#ffdd00",
      scared: "#ff6600",
      worried: "#88aaff",
      focused: "#00ccff",
      annoyed: "#ff7744",
      skeptic: "#88ccff",
      frustrated: "#ff5555",
      unimpressed: "#666699",
      sleepy: "#5577aa",
      suspicious: "#aa7744",
      squint: "#777777",
      neutral: "#00aaff",
      blink_high: "#00aaff",
      blink_low: "#00aaff",
    };
    return colors[emotion] || "#00aaff";
  };

  const getEyePosition = () => {
    const positions = {
      sad_down: { leftY: 108, rightY: 108, leftX: 90, rightX: 150 },
      sad_up: { leftY: 92, rightY: 92, leftX: 90, rightX: 150 },
      suspicious: { leftY: 96, rightY: 104, leftX: 88, rightX: 152 },
      skeptic: { leftY: 104, rightY: 96, leftX: 92, rightX: 148 },
      angry: { leftY: 98, rightY: 98, leftX: 88, rightX: 152 },
      furious: { leftY: 96, rightY: 96, leftX: 86, rightX: 154 },
      scared: { leftY: 95, rightY: 95, leftX: 85, rightX: 155 },
      default: { leftY: 100, rightY: 100, leftX: 90, rightX: 150 },
    };
    return positions[emotion as keyof typeof positions] || positions.default;
  };

  const renderEye = (x: number, y: number, isLeft: boolean) => {
    const eyeShape = getEyeShape();
    const color = getEyeColor();
    const key = isLeft ? "left" : "right";

    switch (eyeShape.shape) {
      case "circle":
        return (
          <motion.circle
            key={key}
            cx={x}
            cy={y}
            r={eyeShape.width / 2}
            fill={color}
            filter="url(#eyeGlow)"
            animate={{
              r: eyeShape.width / 2,
              cy: y,
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        );

      case "diamond":
        return (
          <motion.path
            key={key}
            d={`M ${x} ${y - eyeShape.height / 2} L ${
              x + eyeShape.width / 2
            } ${y} L ${x} ${y + eyeShape.height / 2} L ${
              x - eyeShape.width / 2
            } ${y} Z`}
            fill={color}
            filter="url(#eyeGlow)"
            animate={{
              d: `M ${x} ${y - eyeShape.height / 2} L ${
                x + eyeShape.width / 2
              } ${y} L ${x} ${y + eyeShape.height / 2} L ${
                x - eyeShape.width / 2
              } ${y} Z`,
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        );

      case "rectangle":
        return (
          <motion.rect
            key={key}
            x={x - eyeShape.width / 2}
            y={y - eyeShape.height / 2}
            width={eyeShape.width}
            height={eyeShape.height}
            rx="2"
            fill={color}
            filter="url(#eyeGlow)"
            animate={{
              width: eyeShape.width,
              height: eyeShape.height,
              y: y - eyeShape.height / 2,
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        );

      case "line":
        return (
          <motion.line
            key={key}
            x1={x - eyeShape.width / 2}
            y1={y}
            x2={x + eyeShape.width / 2}
            y2={y}
            stroke={color}
            strokeWidth={eyeShape.height}
            strokeLinecap="round"
            filter="url(#eyeGlow)"
            animate={{
              y1: y,
              y2: y,
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        );

      case "happy":
        // Using SVG path from public/images/happy.svg
        const scale = eyeShape.width / 20; // Scale factor to fit eye size
        const offsetX = x - eyeShape.width / 2;
        const offsetY = y - eyeShape.height / 2;

        // Extract and scale the path from happy.svg (first eye shape)
        const happySvgPath = isLeft
          ? `M ${offsetX + 15.4 * scale} ${
              offsetY + 32.6 * scale
            } c-8.5 -8.4 -9.1 -10.5 -4.3 -15.4 3.8 -3.9 5.8 -4.0 9.7 -0.7 3.7 3.1 4.7 3.1 8.4 0 3.6 -3.1 5.6 -3.1 8.9 -0.2 3.5 3.0 4.8 5.8 3.9 8.2 -1.3 3.4 -14.6 15.5 -17.0 15.5 -1.4 0 -4.9 -2.7 -9.6 -7.4z m17.6 -3.0 l7.1 -6.9 -3.0 -3.1 -3.1 -3.0 -4.5 4.4 -4.5 4.4 -4.5 -4.4 -4.5 -4.4 -3.0 2.9 -3.0 2.9 7.2 7.3 c4.0 4.0 7.6 7.2 8.0 7.1 0.3 -0.2 3.9 -3.4 7.8 -7.2z`
          : `M ${offsetX + 63.3 * scale} ${
              offsetY + 32.6 * scale
            } c-8.4 -8.5 -9.0 -10.8 -4.3 -15.6 3.6 -3.7 5.9 -3.8 9.8 -0.5 3.7 3.1 4.7 3.1 8.4 0 3.9 -3.3 5.8 -3.2 9.7 0.5 4.9 4.8 4.3 7.2 -4.2 15.7 -5.0 5.0 -8.1 7.3 -9.7 7.3 -1.7 0 -4.7 -2.3 -9.7 -7.4z m17.5 -2.9 l7.2 -7.3 -3.0 -2.9 -3.0 -2.9 -4.5 4.4 -4.5 4.4 -4.5 -4.4 -4.5 -4.4 -3.0 2.9 -3.0 2.9 7.2 7.3 c4.0 4.0 7.5 7.3 7.8 7.3 0.3 0 3.8 -3.3 7.8 -7.3z`;

        return (
          <g key={key}>
            <motion.path
              d={happySvgPath}
              fill={color}
              filter="url(#eyeGlow)"
              animate={{
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 0.4,
                ease: "easeOut",
                scale: { duration: 1.5, repeat: Infinity, ease: "easeInOut" },
              }}
              style={{ transformOrigin: `${x}px ${y}px` }}
            />
            {/* Happy sparkle */}
            <motion.circle
              cx={x + eyeShape.width / 4}
              cy={y - eyeShape.height / 4}
              r="1.5"
              fill="rgba(255, 255, 255, 0.8)"
              animate={{
                opacity: [0.8, 0.3, 0.8],
                scale: [1, 1.3, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
                delay: isLeft ? 0 : 0.5,
              }}
            />
          </g>
        );

      case "star":
        const starPath = `M ${x} ${y - eyeShape.height / 2} L ${x + 4} ${
          y - 4
        } L ${x + eyeShape.width / 2} ${y} L ${x + 4} ${y + 4} L ${x} ${
          y + eyeShape.height / 2
        } L ${x - 4} ${y + 4} L ${x - eyeShape.width / 2} ${y} L ${x - 4} ${
          y - 4
        } Z`;
        return (
          <motion.path
            key={key}
            d={starPath}
            fill={color}
            filter="url(#eyeGlow)"
            animate={{
              d: starPath,
              rotate: emotion === "glee" ? 360 : 0,
            }}
            transition={{
              duration: 0.4,
              ease: "easeOut",
              rotate: { duration: 2, repeat: Infinity, ease: "linear" },
            }}
            style={{ transformOrigin: `${x}px ${y}px` }}
          />
        );

      default: // ellipse
        return (
          <motion.ellipse
            key={key}
            cx={x}
            cy={y}
            rx={eyeShape.width / 2}
            ry={eyeShape.height / 2}
            fill={color}
            filter="url(#eyeGlow)"
            animate={{
              ry: eyeShape.height / 2,
              rx: eyeShape.width / 2,
              cy: y,
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        );
    }
  };

  const getMouthPath = () => {
    const paths = {
      happy: "M 75 145 Q 120 170 165 145",
      glee: "M 70 140 Q 120 180 170 140",
      sad_down: "M 75 165 Q 120 140 165 165",
      sad_up: "M 80 160 Q 120 150 160 160",
      angry: "M 75 155 L 165 145",
      furious: "M 70 160 L 170 140",
      surprised: "M 120 145 A 12 12 0 1 1 120 146",
      awe: "M 120 140 A 18 18 0 1 1 120 141",
      scared: "M 105 150 Q 120 140 135 150",
      worried: "M 80 160 Q 120 150 160 160",
      focused: "M 80 152 L 160 152",
      annoyed: "M 80 154 L 160 146",
      skeptic: "M 85 148 Q 120 154 155 148",
      frustrated: "M 75 160 Q 120 145 165 160",
      unimpressed: "M 90 152 L 150 152",
      sleepy: "M 90 152 Q 120 158 150 152",
      suspicious: "M 85 148 Q 120 154 155 148",
      squint: "M 95 152 L 145 152",
      default: "M 80 152 L 160 152",
    };
    return paths[emotion as keyof typeof paths] || paths.default;
  };

  const eyePosition = getEyePosition();

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.div
        animate={{
          scale: 1 + intensity * 0.08,
          rotate: emotion === "furious" ? [-2, 2, -2, 0] : 0,
        }}
        transition={{
          duration: 0.6,
          rotate: { duration: 0.15, repeat: emotion === "furious" ? 4 : 0 },
        }}
      >
        <svg
          ref={svgRef}
          width="280"
          height="280"
          viewBox="0 0 280 280"
          className="drop-shadow-2xl"
          style={{ filter: "drop-shadow(0 0 20px rgba(0, 170, 255, 0.3))" }}
        >
          <defs>
            {/* Enhanced glow effects */}
            <filter id="eyeGlow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur
                stdDeviation={4 + intensity * 3}
                result="coloredBlur"
              />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>

            <filter id="faceGlow" x="-20%" y="-20%" width="140%" height="140%">
              <feGaussianBlur
                stdDeviation={2 + intensity}
                result="coloredBlur"
              />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>

            <filter id="mouthGlow" x="-30%" y="-30%" width="160%" height="160%">
              <feGaussianBlur
                stdDeviation={3 + intensity * 2}
                result="coloredBlur"
              />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>

            {/* Gradient for face */}
            <radialGradient id="faceGradient" cx="50%" cy="45%" r="60%">
              <stop offset="0%" stopColor="#1a1a1a" />
              <stop offset="100%" stopColor="#0a0a0a" />
            </radialGradient>
          </defs>

          {/* Background glow circle */}
          <motion.circle
            cx="140"
            cy="140"
            r="120"
            fill="none"
            stroke={getEyeColor()}
            strokeWidth="1"
            opacity="0.1"
            filter="url(#faceGlow)"
            animate={{
              r: 120 + intensity * 10,
              opacity: 0.1 + intensity * 0.1,
            }}
            transition={{ duration: 0.5 }}
          />

          {/* Main face container - more Vector-like rounded rectangle */}
          <motion.rect
            x="50"
            y="60"
            width="180"
            height="160"
            rx="25"
            ry="20"
            fill="url(#faceGradient)"
            stroke={getEyeColor()}
            strokeWidth="2"
            filter="url(#faceGlow)"
            animate={{
              strokeWidth: 2 + intensity * 1.5,
              rx: 25 + intensity * 3,
            }}
            transition={{ duration: 0.4 }}
          />

          {/* Screen effect lines */}
          <motion.line
            x1="60"
            y1="75"
            x2="220"
            y2="75"
            stroke={getEyeColor()}
            strokeWidth="0.5"
            opacity="0.3"
            animate={{ opacity: 0.2 + intensity * 0.2 }}
          />
          <motion.line
            x1="60"
            y1="205"
            x2="220"
            y2="205"
            stroke={getEyeColor()}
            strokeWidth="0.5"
            opacity="0.3"
            animate={{ opacity: 0.2 + intensity * 0.2 }}
          />

          {/* Eyes */}
          {renderEye(eyePosition.leftX, eyePosition.leftY, true)}
          {renderEye(eyePosition.rightX, eyePosition.rightY, false)}

          {/* Eye reflections for more depth */}
          <motion.ellipse
            cx={eyePosition.leftX - 3}
            cy={eyePosition.leftY - 3}
            rx="2"
            ry="3"
            fill="rgba(255, 255, 255, 0.6)"
            animate={{
              opacity: isBlinking ? 0 : 0.6,
              rx: getEyeShape().width * 0.15,
              ry: getEyeShape().height * 0.2,
            }}
            transition={{ duration: 0.3 }}
          />
          <motion.ellipse
            cx={eyePosition.rightX - 3}
            cy={eyePosition.rightY - 3}
            rx="2"
            ry="3"
            fill="rgba(255, 255, 255, 0.6)"
            animate={{
              opacity: isBlinking ? 0 : 0.6,
              rx: getEyeShape().width * 0.15,
              ry: getEyeShape().height * 0.2,
            }}
            transition={{ duration: 0.3 }}
          />

          {/* Mouth */}
          <motion.path
            d={getMouthPath()}
            stroke={getEyeColor()}
            strokeWidth={3 + intensity * 1.5}
            strokeLinecap="round"
            fill="none"
            filter="url(#mouthGlow)"
            animate={{
              d: getMouthPath(),
              strokeWidth: 3 + intensity * 1.5,
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />

          {/* Antenna with more Vector-like design */}
          <motion.line
            x1="140"
            y1="60"
            x2="140"
            y2="35"
            stroke={getEyeColor()}
            strokeWidth="4"
            strokeLinecap="round"
            filter="url(#eyeGlow)"
            animate={{
              y2: emotion === "surprised" || emotion === "awe" ? 25 : 35,
              strokeWidth: 4 + intensity,
            }}
            transition={{ duration: 0.4 }}
          />

          {/* Antenna tip */}
          <motion.circle
            cx="140"
            cy={emotion === "surprised" || emotion === "awe" ? 20 : 30}
            r={emotion === "awe" ? 8 : 6}
            fill={getEyeColor()}
            filter="url(#eyeGlow)"
            animate={{
              cy: emotion === "surprised" || emotion === "awe" ? 20 : 30,
              r: emotion === "awe" ? 8 : 6,
              scale: emotion === "glee" ? [1, 1.2, 1] : 1,
            }}
            transition={{
              duration: 0.4,
              scale: {
                duration: 0.8,
                repeat: emotion === "glee" ? Infinity : 0,
              },
            }}
          />

          {/* Particle effects for extreme emotions */}
          <AnimatePresence>
            {eyeParticles.map((particle) => (
              <motion.circle
                key={particle.id}
                cx={particle.x}
                cy={particle.y}
                r="2"
                fill={getEyeColor()}
                initial={{ opacity: 0, scale: 0 }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                  y: particle.y - 20,
                }}
                exit={{ opacity: 0 }}
                transition={{
                  duration: 1.5,
                  delay: particle.id * 0.1,
                  ease: "easeOut",
                }}
              />
            ))}
          </AnimatePresence>

          {/* Side panels for more tech look */}
          <motion.rect
            x="35"
            y="90"
            width="8"
            height="100"
            rx="4"
            fill={getEyeColor()}
            opacity="0.3"
            animate={{
              opacity: 0.2 + intensity * 0.2,
              height: 100 + intensity * 20,
            }}
            transition={{ duration: 0.4 }}
          />
          <motion.rect
            x="237"
            y="90"
            width="8"
            height="100"
            rx="4"
            fill={getEyeColor()}
            opacity="0.3"
            animate={{
              opacity: 0.2 + intensity * 0.2,
              height: 100 + intensity * 20,
            }}
            transition={{ duration: 0.4 }}
          />

          {/* Status indicators */}
          <motion.circle
            cx="70"
            cy="85"
            r="3"
            fill={getEyeColor()}
            opacity="0.7"
            animate={{
              opacity: [0.7, 0.3, 0.7],
              scale: [1, 0.8, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.circle
            cx="210"
            cy="85"
            r="3"
            fill={getEyeColor()}
            opacity="0.7"
            animate={{
              opacity: [0.3, 0.7, 0.3],
              scale: [0.8, 1, 0.8],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          />
        </svg>
      </motion.div>
    </div>
  );
};
