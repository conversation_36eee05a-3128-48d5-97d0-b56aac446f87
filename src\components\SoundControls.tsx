'use client';

import { useState, useEffect } from 'react';

interface SoundControlsProps {
  isSoundEnabled: boolean;
  onSoundToggle: (enabled: boolean) => void;
}

export const SoundControls = ({ isSoundEnabled, onSoundToggle }: SoundControlsProps) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Load sound preference from localStorage
    const savedPreference = localStorage.getItem('robotSoundEnabled');
    if (savedPreference !== null) {
      onSoundToggle(savedPreference === 'true');
    }
  }, [onSoundToggle]);

  const handleToggle = () => {
    const newState = !isSoundEnabled;
    onSoundToggle(newState);
    if (isClient) {
      localStorage.setItem('robotSoundEnabled', newState.toString());
    }
  };

  if (!isClient) return null;

  return (
    <div className="flex items-center justify-between p-4 bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-600/30">
      <div className="flex items-center gap-3">
        <span className="text-lg">🎵</span>
        <span className="text-sm font-medium text-cyan-300">Sound Effects:</span>
      </div>
      
      <div className="flex items-center gap-4">
        <button
          onClick={handleToggle}
          className={`relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-300 transform hover:scale-105 ${
            isSoundEnabled 
              ? 'bg-gradient-to-r from-blue-600 to-cyan-600 shadow-lg' 
              : 'bg-gray-600/50 border border-gray-500/50'
          }`}
          style={isSoundEnabled ? { boxShadow: '0 0 15px rgba(0, 170, 255, 0.4)' } : {}}
        >
          <span
            className={`inline-block h-5 w-5 transform rounded-full bg-white transition-all duration-300 shadow-md ${
              isSoundEnabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
        
        <span className={`text-sm font-medium transition-colors duration-300 ${
          isSoundEnabled ? 'text-cyan-400' : 'text-gray-400'
        }`}>
          {isSoundEnabled ? '🔊 Enabled' : '🔇 Disabled'}
        </span>
      </div>
    </div>
  );
};