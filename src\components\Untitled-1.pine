// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @version=5
indicator("Order Blocks + Supertrend", overlay=true, max_boxes_count=500, max_labels_count=500, max_lines_count=500, max_bars_back=5000)

// --- Order Blocks Inputs ---
showInvalidated = input.bool(true, "Show Historic OB Zones", group="Order Blocks Configuration")
orderBlockVolumetricInfo = input.bool(true, "OB Volumetric Info", group="Order Blocks Configuration")
obEndMethod = input.string("Wick", "OB Zone Invalidation", options=["Wick", "Close"], group="Order Blocks Configuration")
combineOBs = input.bool(true, "Combine OB Zones", group="Order Blocks Configuration")
maxATRMult = input.float(3.5, "Max ATR Multiplier", group="Order Blocks Configuration")
swingLength = input.int(10, "Swing Length", minval=3, group="Order Blocks Configuration")
zoneCount = input.string("Low", "Zone Count", options=["High", "Medium", "Low", "One"], group="Order Blocks Configuration")
bullOrderBlockColor = input.color(#08998180, "Bullish OB Color", group="Order Blocks Style")
bearOrderBlockColor = input.color(#f2364680, "Bearish OB Color", group="Order Blocks Style")
textColor = input.color(#ffffff80, "Text Color", group="Order Blocks Style")
extendZonesBy = input.int(15, "Extend Zones", minval=1, maxval=30, group="Order Blocks Style")
extendZonesDynamic = input.bool(true, "Dynamic Zone Extension", group="Order Blocks Style")
volumeBarsPlace = input.string("Left", "Show Volume Bars At", options=["Left", "Right"], group="Order Blocks Style")
mirrorVolumeBars = input.bool(true, "Mirror Volume Bars", group="Order Blocks Style")

bullishOrderBlocksCount = zoneCount == "One" ? 1 : zoneCount == "Low" ? 3 : zoneCount == "Medium" ? 5 : 10
bearishOrderBlocksCount = zoneCount == "One" ? 1 : zoneCount == "Low" ? 3 : zoneCount == "Medium" ? 5 : 10
extendZonesByTime = extendZonesBy * timeframe.in_seconds(timeframe.period) * 1000
atr_ob = ta.atr(10)

// --- Supertrend Inputs ---
Periods = input.int(10, title="ATR Period", group="Supertrend Configuration")
src = input.source(hl2, title="Source", group="Supertrend Configuration")
Multiplier = input.float(3.0, title="ATR Multiplier", step=0.1, group="Supertrend Configuration")
changeATR = input.bool(true, title="Change ATR Calculation Method?", group="Supertrend Configuration")
showsignals = input.bool(true, title="Show Supertrend Signals?", group="Supertrend Configuration")
highlighting = input.bool(true, title="Supertrend Highlighter On/Off?", group="Supertrend Configuration")

// --- Supertrend Calculations ---
atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = src - (Multiplier * atr)
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = src + (Multiplier * atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
var int trend = 1  // Declare trend variable with initial value
trend := nz(trend[1], 1)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// --- Order Blocks Types ---
type orderBlockInfo
    float top
    float bottom
    float obVolume
    string obType
    int startTime
    float bbVolume
    float obLowVolume
    float obHighVolume
    bool breaker
    int breakTime
    string timeframeStr
    bool disabled = false
    string combinedTimeframesStr = na
    bool combined = false

type orderBlock
    orderBlockInfo info
    bool isRendered = false
    box orderBox = na
    box orderBoxText = na
    box orderBoxPositive = na
    box orderBoxNegative = na
    line orderSeperator = na
    line orderTextSeperator = na

type obSwing
    int x = na
    float y = na
    float swingVolume = na
    bool crossed = false

// --- Order Blocks Arrays ---
var bullishOrderBlocksList = array.new<orderBlockInfo>(0)
var bearishOrderBlocksList = array.new<orderBlockInfo>(0)
var allOrderBlocksList = array.new<orderBlock>(0)

// --- Order Blocks Functions ---
createOrderBlock(orderBlockInfo orderBlockInfoF) =>
    orderBlock newOrderBlock = orderBlock.new(orderBlockInfoF)
    newOrderBlock

safeDeleteOrderBlock(orderBlock orderBlockF) =>
    orderBlockF.isRendered := false
    box.delete(orderBlockF.orderBox)
    box.delete(orderBlockF.orderBoxText)
    box.delete(orderBlockF.orderBoxPositive)
    box.delete(orderBlockF.orderBoxNegative)
    line.delete(orderBlockF.orderSeperator)
    line.delete(orderBlockF.orderTextSeperator)

colorWithTransparency(colorF, transparencyX) =>
    color.new(colorF, color.t(colorF) * transparencyX)

createOBBox(boxColor, transparencyX = 1.0, xlocType = xloc.bar_time) =>
    box.new(na, na, na, na, text_size=size.normal, xloc=xlocType, extend=extend.none, bgcolor=colorWithTransparency(boxColor, transparencyX), text_color=textColor, text_halign=text.align_center, border_color=#00000000)

renderOrderBlock(orderBlock ob) =>
    orderBlockInfo info = ob.info
    ob.isRendered := true
    orderColor = info.obType == "Bull" ? bullOrderBlockColor : bearOrderBlockColor
    if not info.breaker or showInvalidated
        ob.orderBox := createOBBox(orderColor, 1.5)
        if info.combined
            ob.orderBox.set_bgcolor(colorWithTransparency(orderColor, 1.1))
        ob.orderBoxText := createOBBox(color.new(color.white, 100))
        if orderBlockVolumetricInfo
            ob.orderBoxPositive := createOBBox(bullOrderBlockColor)
            ob.orderBoxNegative := createOBBox(bearOrderBlockColor)
            ob.orderSeperator := line.new(na, na, na, na, xloc.bar_time, extend.none, textColor, line.style_dashed, 1)
            ob.orderTextSeperator := line.new(na, na, na, na, xloc.bar_time, extend.none, textColor, line.style_solid, 1)
        zoneSize = extendZonesDynamic ? na(info.breakTime) ? extendZonesByTime : (info.breakTime - info.startTime) : extendZonesByTime
        if na(info.breakTime)
            zoneSize := (time + 1) - info.startTime
        startX = volumeBarsPlace == "Left" ? info.startTime : info.startTime + zoneSize - zoneSize / 3
        maxEndX = volumeBarsPlace == "Left" ? info.startTime + zoneSize / 3 : info.startTime + zoneSize
        box.set_lefttop(ob.orderBox, info.startTime, info.top)
        box.set_rightbottom(ob.orderBox, info.startTime + zoneSize, info.bottom)
        box.set_lefttop(ob.orderBoxText, volumeBarsPlace == "Left" ? maxEndX : info.startTime, info.top)
        box.set_rightbottom(ob.orderBoxText, volumeBarsPlace == "Left" ? info.startTime + zoneSize : startX, info.bottom)
        percentage = int((math.min(info.obHighVolume, info.obLowVolume) / math.max(info.obHighVolume, info.obLowVolume)) * 100.0)
        OBText = (na(info.combinedTimeframesStr) ? info.timeframeStr : info.combinedTimeframesStr) + " OB"
        box.set_text(ob.orderBoxText, (orderBlockVolumetricInfo ? str.tostring(info.obVolume, format.volume) + " (" + str.tostring(percentage) + "%)\n" : "") + OBText)
        if orderBlockVolumetricInfo
            curEndXHigh = int(math.ceil((info.obHighVolume / info.obVolume) * (maxEndX - startX) + startX))
            curEndXLow = int(math.ceil((info.obLowVolume / info.obVolume) * (maxEndX - startX) + startX))
            box.set_lefttop(ob.orderBoxPositive, mirrorVolumeBars ? startX : curEndXLow, info.top)
            box.set_rightbottom(ob.orderBoxPositive, mirrorVolumeBars ? curEndXHigh : maxEndX, (info.bottom + info.top) / 2)
            box.set_lefttop(ob.orderBoxNegative, mirrorVolumeBars ? startX : curEndXHigh, info.bottom)
            box.set_rightbottom(ob.orderBoxNegative, mirrorVolumeBars ? curEndXLow : maxEndX, (info.bottom + info.top) / 2)
            line.set_xy1(ob.orderSeperator, volumeBarsPlace == "Left" ? startX : maxEndX, (info.bottom + info.top) / 2)
            line.set_xy2(ob.orderSeperator, volumeBarsPlace == "Left" ? maxEndX : startX, (info.bottom + info.top) / 2)
            line.set_xy1(ob.orderTextSeperator, volumeBarsPlace == "Left" ? maxEndX : startX, info.top)
            line.set_xy2(ob.orderTextSeperator, volumeBarsPlace == "Left" ? maxEndX : startX, info.bottom)

findOBSwings(len) =>
    var swingType = 0
    var obSwing top = obSwing.new(na, na)
    var obSwing bottom = obSwing.new(na, na)
    upper = ta.highest(len)
    lower = ta.lowest(len)
    swingType := high[len] > upper ? 0 : low[len] < lower ? 1 : swingType
    if swingType == 0 and swingType[1] != 0
        top := obSwing.new(bar_index[len], high[len], volume[len])
    if swingType == 1 and swingType[1] != 1
        bottom := obSwing.new(bar_index[len], low[len], volume[len])
    [top, bottom]

findOrderBlocks() =>
    if bar_index > last_bar_index - 1750
        [top, btm] = findOBSwings(swingLength)
        useBody = false
        max = useBody ? math.max(close, open) : high
        min = useBody ? math.min(close, open) : low
        // Bullish Order Block
        if bullishOrderBlocksList.size() > 0
            for i = bullishOrderBlocksList.size() - 1 to 0
                currentOB = bullishOrderBlocksList.get(i)
                if not currentOB.breaker
                    if (obEndMethod == "Wick" ? low : math.min(open, close)) < currentOB.bottom
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if high > currentOB.top
                        bullishOrderBlocksList.remove(i)
        if close > top.y and not top.crossed
            top.crossed := true
            boxBtm = max[1]
            boxTop = min[1]
            boxLoc = time[1]
            for i = 1 to (bar_index - top.x) - 1
                boxBtm := math.min(min[i], boxBtm)
                boxTop := boxBtm == min[i] ? max[i] : boxTop
                boxLoc := boxBtm == min[i] ? time[i] : boxLoc
            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bull", boxLoc)
            newOrderBlockInfo.obLowVolume := volume[2]
            newOrderBlockInfo.obHighVolume := volume + volume[1]
            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr_ob * maxATRMult
                bullishOrderBlocksList.unshift(newOrderBlockInfo)
                if bullishOrderBlocksList.size() > 30
                    bullishOrderBlocksList.pop()
        // Bearish Order Block
        if bearishOrderBlocksList.size() > 0
            for i = bearishOrderBlocksList.size() - 1 to 0
                currentOB = bearishOrderBlocksList.get(i)
                if not currentOB.breaker
                    if (obEndMethod == "Wick" ? high : math.max(open, close)) > currentOB.top
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if low < currentOB.bottom
                        bearishOrderBlocksList.remove(i)
        if close < btm.y and not btm.crossed
            btm.crossed := true
            boxBtm = min[1]
            boxTop = max[1]
            boxLoc = time[1]
            for i = 1 to (bar_index - btm.x) - 1
                boxTop := math.max(max[i], boxTop)
                boxBtm := boxTop == max[i] ? min[i] : boxBtm
                boxLoc := boxTop == max[i] ? time[i] : boxLoc
            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bear", boxLoc)
            newOrderBlockInfo.obLowVolume := volume + volume[1]
            newOrderBlockInfo.obHighVolume := volume[2]
            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr_ob * maxATRMult
                bearishOrderBlocksList.unshift(newOrderBlockInfo)
                if bearishOrderBlocksList.size() > 30
                    bearishOrderBlocksList.pop()
    true

areaOfOB(orderBlockInfo OBInfoF) =>
    float XA1 = OBInfoF.startTime
    float XA2 = na(OBInfoF.breakTime) ? time + 1 : OBInfoF.breakTime
    float YA1 = OBInfoF.top
    float YA2 = OBInfoF.bottom
    float edge1 = math.sqrt((XA2 - XA1) * (XA2 - XA1))
    float edge2 = math.sqrt((YA2 - YA1) * (YA2 - YA1))
    float totalArea = edge1 * edge2
    totalArea

doOBsTouch(orderBlockInfo OBInfo1, orderBlockInfo OBInfo2) =>
    float XA1 = OBInfo1.startTime
    float XA2 = na(OBInfo1.breakTime) ? time + 1 : OBInfo1.breakTime
    float YA1 = OBInfo1.top
    float YA2 = OBInfo1.bottom
    float XB1 = OBInfo2.startTime
    float XB2 = na(OBInfo2.breakTime) ? time + 1 : OBInfo2.breakTime
    float YB1 = OBInfo2.top
    float YB2 = OBInfo2.bottom
    float intersectionArea = math.max(0, math.min(XA2, XB2) - math.max(XA1, XB1)) * math.max(0, math.min(YA1, YB1) - math.max(YA2, YB2))
    float unionArea = areaOfOB(OBInfo1) + areaOfOB(OBInfo2) - intersectionArea
    float overlapPercentage = (intersectionArea / unionArea) * 100.0
    overlapPercentage > 0

isOBValid(orderBlockInfo OBInfo) =>
    valid = true
    if OBInfo.disabled
        valid := false
    valid

combineOBsFunc() =>
    if allOrderBlocksList.size() > 0
        lastCombinations = 999
        while lastCombinations > 0
            lastCombinations := 0
            for i = 0 to allOrderBlocksList.size() - 1
                curOB1 = allOrderBlocksList.get(i)
                for j = 0 to allOrderBlocksList.size() - 1
                    curOB2 = allOrderBlocksList.get(j)
                    if i == j
                        continue
                    if not isOBValid(curOB1.info) or not isOBValid(curOB2.info)
                        continue
                    if curOB1.info.obType != curOB2.info.obType
                        continue
                    if doOBsTouch(curOB1.info, curOB2.info)
                        curOB1.info.disabled := true
                        curOB2.info.disabled := true
                        orderBlock newOB = createOrderBlock(orderBlockInfo.new(math.max(curOB1.info.top, curOB2.info.top), math.min(curOB1.info.bottom, curOB2.info.bottom), curOB1.info.obVolume + curOB2.info.obVolume, curOB1.info.obType))
                        newOB.info.startTime := math.min(curOB1.info.startTime, curOB2.info.startTime)
                        newOB.info.breakTime := math.max(nz(curOB1.info.breakTime), nz(curOB2.info.breakTime))
                        newOB.info.breakTime := newOB.info.breakTime == 0 ? na : newOB.info.breakTime
                        newOB.info.timeframeStr := curOB1.info.timeframeStr
                        newOB.info.obVolume := curOB1.info.obVolume + curOB2.info.obVolume
                        newOB.info.obLowVolume := curOB1.info.obLowVolume + curOB2.info.obLowVolume
                        newOB.info.obHighVolume := curOB1.info.obHighVolume + curOB2.info.obHighVolume
                        newOB.info.bbVolume := nz(curOB1.info.bbVolume, 0) + nz(curOB2.info.bbVolume, 0)
                        newOB.info.breaker := curOB1.info.breaker or curOB2.info.breaker
                        newOB.info.combined := true
                        if timeframe.in_seconds(curOB1.info.timeframeStr) != timeframe.in_seconds(curOB2.info.timeframeStr)
                            newOB.info.combinedTimeframesStr := (na(curOB1.info.combinedTimeframesStr) ? curOB1.info.timeframeStr : curOB1.info.combinedTimeframesStr) + " & " + (na(curOB2.info.combinedTimeframesStr) ? curOB2.info.timeframeStr : curOB2.info.combinedTimeframesStr)
                        allOrderBlocksList.unshift(newOB)
                        lastCombinations += 1

handleOrderBlocksFinal() =>
    if allOrderBlocksList.size() > 0
        for i = 0 to allOrderBlocksList.size() - 1
            safeDeleteOrderBlock(allOrderBlocksList.get(i))
    allOrderBlocksList.clear()
    for j = 0 to math.min(bullishOrderBlocksList.size() - 1, bullishOrderBlocksCount - 1)
        orderBlockInfoF = bullishOrderBlocksList.get(j)
        orderBlockInfoF.timeframeStr := timeframe.period
        allOrderBlocksList.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))
    for j = 0 to math.min(bearishOrderBlocksList.size() - 1, bearishOrderBlocksCount - 1)
        orderBlockInfoF = bearishOrderBlocksList.get(j)
        orderBlockInfoF.timeframeStr := timeframe.period
        allOrderBlocksList.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))
    if combineOBs
        combineOBsFunc()
    if allOrderBlocksList.size() > 0
        for i = 0 to allOrderBlocksList.size() - 1
            curOB = allOrderBlocksList.get(i)
            if isOBValid(curOB.info)
                renderOrderBlock(curOB)

// --- Combined Signal Logic ---
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1
var bool inBullOB = false
var bool inBearOB = false
var float entryPrice = na
var float slPrice = na
var line slLine = na
var bool buyAlert = false
var bool sellAlert = false

if bullishOrderBlocksList.size() > 0
    for i = 0 to math.min(bullishOrderBlocksList.size() - 1, bullishOrderBlocksCount - 1)
        ob = bullishOrderBlocksList.get(i)
        if not ob.breaker and close >= ob.bottom and close <= ob.top
            inBullOB := true
            break
        else
            inBullOB := false

if bearishOrderBlocksList.size() > 0
    for i = 0 to math.min(bearishOrderBlocksList.size() - 1, bearishOrderBlocksCount - 1)
        ob = bearishOrderBlocksList.get(i)
        if not ob.breaker and close >= ob.bottom and close <= ob.top
            inBearOB := true
            break
        else
            inBearOB := false

buyAlert := buySignal and inBullOB
sellAlert := sellSignal and inBearOB

if buyAlert
    entryPrice := close
    slPrice := up
    label.new(bar_index, close, "Buy\n" + str.tostring(close, "#.##") + "\nSL: " + str.tostring(up, "#.##"), color=color.green, textcolor=color.white, style=label.style_label_down, size=size.small)
    line.delete(slLine[1])
    slLine := line.new(bar_index, up, bar_index + 100, up, color=color.red, style=line.style_dashed)

if sellAlert
    entryPrice := close
    slPrice := dn
    label.new(bar_index, close, "Sell\n" + str.tostring(close, "#.##") + "\nSL: " + str.tostring(dn, "#.##"), color=color.red, textcolor=color.white, style=label.style_label_up, size=size.small)
    line.delete(slLine[1])
    slLine := line.new(bar_index, dn, bar_index + 100, dn, color=color.green, style=line.style_dashed)

// --- Alerts in Global Scope ---
alertcondition(buyAlert, title="OB + Supertrend Buy", message="OB + Supertrend Buy Signal")
alertcondition(sellAlert, title="OB + Supertrend Sell", message="OB + Supertrend Sell Signal")

// --- Supertrend Visualization ---
upPlot = plot(trend == 1 ? up : na, title="Up Trend", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot = plot(trend == -1 ? dn : na, title="Down Trend", style=plot.style_linebr, linewidth=2, color=color.red)
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=0)
longFillColor = highlighting ? (trend == 1 ? color.new(color.green, 80) : color.new(color.white, 100)) : color.new(color.white, 100)
shortFillColor = highlighting ? (trend == -1 ? color.new(color.red, 80) : color.new(color.white, 100)) : color.new(color.white, 100)
fill(mPlot, upPlot, title="UpTrend Highlighter", color=longFillColor)
fill(mPlot, dnPlot, title="DownTrend Highlighter", color=shortFillColor)

// --- Execute Order Blocks Logic ---
findOrderBlocks()
if barstate.isconfirmed
    handleOrderBlocksFinal()