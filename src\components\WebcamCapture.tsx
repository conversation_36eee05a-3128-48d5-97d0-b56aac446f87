'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { playCaptureSound } from '@/lib/soundEffects';

interface WebcamCaptureProps {
  isSoundEnabled?: boolean;
}

export const WebcamCapture = ({ isSoundEnabled = true }: WebcamCaptureProps) => {
  const [isWebcamActive, setIsWebcamActive] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startWebcam = useCallback(async () => {
    try {
      setError(null);
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user' // Front camera for selfies
        } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsWebcamActive(true);
      }
    } catch (err) {
      console.error('Error accessing webcam:', err);
      setError('Could not access webcam. Please check permissions.');
    }
  }, []);

  const stopWebcam = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setIsWebcamActive(false);
  }, []);

  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return;

    setIsCapturing(true);
    
    // Play capture sound
    if (isSoundEnabled) {
      try {
        await playCaptureSound();
      } catch (error) {
        console.error('Failed to play capture sound:', error);
      }
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // Set canvas size to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw video frame to canvas
      ctx.drawImage(video, 0, 0);

      // Convert to blob and create download link
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setCapturedImage(url);
          
          // Auto download
          const link = document.createElement('a');
          link.href = url;
          link.download = `webcam-photo-${Date.now()}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
        setIsCapturing(false);
      }, 'image/png');
    }
  }, [isSoundEnabled]);

  const sharePhoto = useCallback(async () => {
    if (!capturedImage || !navigator.share) return;

    try {
      // Convert data URL to blob
      const response = await fetch(capturedImage);
      const blob = await response.blob();
      const file = new File([blob], `webcam-photo-${Date.now()}.png`, { type: 'image/png' });

      await navigator.share({
        title: 'My Photo',
        text: 'Check out this photo I took!',
        files: [file]
      });
    } catch (error) {
      console.error('Error sharing photo:', error);
    }
  }, [capturedImage]);

  const retakePhoto = useCallback(() => {
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
      setCapturedImage(null);
    }
  }, [capturedImage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopWebcam();
      if (capturedImage) {
        URL.revokeObjectURL(capturedImage);
      }
    };
  }, [stopWebcam, capturedImage]);

  if (error) {
    return (
      <div className="text-center p-6 bg-red-900/30 border border-red-600 rounded-lg">
        <p className="text-red-400 mb-4">❌ {error}</p>
        <button
          onClick={() => setError(null)}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-300 mb-4">📸 Webcam Photo</h3>
        
        {/* Video preview */}
        <div className="relative inline-block">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className={`rounded-lg border-2 border-gray-600 ${
              isWebcamActive ? 'block' : 'hidden'
            }`}
            style={{ maxWidth: '100%', height: 'auto' }}
          />
          
          {/* Captured image preview */}
          {capturedImage && (
            <img
              src={capturedImage}
              alt="Captured"
              className="rounded-lg border-2 border-green-600 max-w-full h-auto"
            />
          )}
          
          {/* Placeholder when no webcam */}
          {!isWebcamActive && !capturedImage && (
            <div className="w-80 h-60 bg-gray-800 border-2 border-gray-600 rounded-lg flex items-center justify-center">
              <span className="text-gray-400">📷 Camera Preview</span>
            </div>
          )}
        </div>

        {/* Hidden canvas for capture */}
        <canvas ref={canvasRef} className="hidden" />
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-2 justify-center">
        {!isWebcamActive && !capturedImage && (
          <button
            onClick={startWebcam}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            📹 Start Camera
          </button>
        )}

        {isWebcamActive && !capturedImage && (
          <>
            <button
              onClick={capturePhoto}
              disabled={isCapturing}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
            >
              {isCapturing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Capturing...
                </>
              ) : (
                <>📸 Take Photo</>
              )}
            </button>
            
            <button
              onClick={stopWebcam}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors"
            >
              ⏹️ Stop Camera
            </button>
          </>
        )}

        {capturedImage && (
          <>
            <button
              onClick={retakePhoto}
              className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-lg transition-colors"
            >
              🔄 Retake
            </button>
            
            {navigator.share && (
              <button
                onClick={sharePhoto}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
              >
                📤 Share
              </button>
            )}
            
            <button
              onClick={stopWebcam}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
            >
              ✅ Done
            </button>
          </>
        )}
      </div>

      <p className="text-xs text-gray-500 text-center">
        Photos are automatically downloaded to your device
      </p>
    </div>
  );
};