"use client";

import { useEffect, useState, useCallback } from "react";

interface ShakeOptions {
  threshold?: number;
  timeout?: number;
}

export const useShake = (onShake: () => void, options: ShakeOptions = {}) => {
  const { threshold = 15, timeout = 500 } = options;
  const [isSupported, setIsSupported] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [lastShake, setLastShake] = useState(0);

  const requestPermission = useCallback(async () => {
    if (
      typeof DeviceMotionEvent !== "undefined" &&
      "requestPermission" in DeviceMotionEvent
    ) {
      try {
        const permission = await (DeviceMotionEvent as any).requestPermission();
        setHasPermission(permission === "granted");
        return permission === "granted";
      } catch (error) {
        console.error("Permission request failed:", error);
        return false;
      }
    }
    return true; // Android or older iOS
  }, []);

  useEffect(() => {
    setIsSupported(typeof DeviceMotionEvent !== "undefined");

    if (typeof DeviceMotionEvent === "undefined") return;

    let lastX = 0,
      lastY = 0,
      lastZ = 0;

    const handleMotion = (event: DeviceMotionEvent) => {
      const { x = 0, y = 0, z = 0 } = event.accelerationIncludingGravity || {};

      const deltaX = Math.abs(x - lastX);
      const deltaY = Math.abs(y - lastY);
      const deltaZ = Math.abs(z - lastZ);

      const totalDelta = deltaX + deltaY + deltaZ;

      if (totalDelta > threshold) {
        const now = Date.now();
        if (now - lastShake > timeout) {
          setLastShake(now);
          onShake();
        }
      }

      lastX = x;
      lastY = y;
      lastZ = z;
    };

    if (hasPermission || !("requestPermission" in DeviceMotionEvent)) {
      window.addEventListener("devicemotion", handleMotion);
    }

    return () => {
      window.removeEventListener("devicemotion", handleMotion);
    };
  }, [onShake, threshold, timeout, hasPermission, lastShake]);

  return {
    isSupported,
    hasPermission,
    requestPermission,
  };
};
