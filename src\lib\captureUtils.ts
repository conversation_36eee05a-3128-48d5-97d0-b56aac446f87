export const captureRobotFace = async (
  svgElement: SVGSVGElement,
  emotion: string
): Promise<void> => {
  try {
    // Create a canvas element
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) throw new Error("Could not get canvas context");

    // Set canvas size
    canvas.width = 480; // 2x the SVG size for better quality
    canvas.height = 480;

    // Create an image from the SVG
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const svgBlob = new Blob([svgData], {
      type: "image/svg+xml;charset=utf-8",
    });
    const svgUrl = URL.createObjectURL(svgBlob);

    // Create image element
    const img = new Image();

    return new Promise((resolve, reject) => {
      img.onload = () => {
        // Fill background with dark color
        ctx.fillStyle = "#1f2937"; // gray-800
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw the SVG image
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Add timestamp and emotion text
        ctx.fillStyle = "#ffffff";
        ctx.font = "24px Arial";
        ctx.textAlign = "center";
        ctx.fillText(
          `Emotion: ${emotion}`,
          canvas.width / 2,
          canvas.height - 60
        );

        ctx.font = "16px Arial";
        ctx.fillText(
          `Captured: ${new Date().toLocaleString()}`,
          canvas.width / 2,
          canvas.height - 30
        );

        // Convert to blob and download
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `robot-face-${emotion}-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }
          URL.revokeObjectURL(svgUrl);
          resolve();
        }, "image/png");
      };

      img.onerror = () => {
        URL.revokeObjectURL(svgUrl);
        reject(new Error("Failed to load SVG image"));
      };

      img.src = svgUrl;
    });
  } catch (error) {
    console.error("Error capturing robot face:", error);
    throw error;
  }
};

export const shareRobotFace = async (
  svgElement: SVGSVGElement,
  emotion: string
): Promise<void> => {
  if (!navigator.share) {
    throw new Error("Web Share API not supported");
  }

  try {
    // Create a canvas for sharing
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) throw new Error("Could not get canvas context");

    canvas.width = 480;
    canvas.height = 480;

    const svgData = new XMLSerializer().serializeToString(svgElement);
    const svgBlob = new Blob([svgData], {
      type: "image/svg+xml;charset=utf-8",
    });
    const svgUrl = URL.createObjectURL(svgBlob);

    const img = new Image();

    return new Promise((resolve, reject) => {
      img.onload = async () => {
        ctx.fillStyle = "#1f2937";
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        ctx.fillStyle = "#ffffff";
        ctx.font = "24px Arial";
        ctx.textAlign = "center";
        ctx.fillText(
          `Robot Emotion: ${emotion}`,
          canvas.width / 2,
          canvas.height - 30
        );

        canvas.toBlob(async (blob) => {
          if (blob) {
            const file = new File([blob], `robot-${emotion}.png`, {
              type: "image/png",
            });

            try {
              await navigator.share({
                title: `Robot Face - ${emotion}`,
                text: `Check out this robot expression: ${emotion}!`,
                files: [file],
              });
            } catch (shareError) {
              console.error("Error sharing:", shareError);
              reject(shareError);
            }
          }
          URL.revokeObjectURL(svgUrl);
          resolve();
        }, "image/png");
      };

      img.onerror = () => {
        URL.revokeObjectURL(svgUrl);
        reject(new Error("Failed to load SVG image"));
      };

      img.src = svgUrl;
    });
  } catch (error) {
    console.error("Error sharing robot face:", error);
    throw error;
  }
};
