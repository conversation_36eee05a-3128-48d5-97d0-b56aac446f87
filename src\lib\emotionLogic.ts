import { EmotionType } from "@/utils/emotionTypes";

const EMOTION_KEYWORDS: Record<string, EmotionType> = {
  // Happy emotions
  happy: "happy",
  joy: "happy",
  smile: "happy",
  good: "happy",
  great: "happy",
  nice: "happy",

  // Glee (extreme happiness)
  glee: "glee",
  excited: "glee",
  awesome: "glee",
  amazing: "glee",
  fantastic: "glee",
  wonderful: "glee",
  love: "glee",

  // Sad emotions
  sad: "sad_down",
  cry: "sad_down",
  upset: "sad_down",
  down: "sad_down",
  depressed: "sad_down",
  hurt: "sad_down",
  disappointed: "sad_down",

  // Sad looking up (hopeful sadness)
  "look up": "sad_up",
  hope: "sad_up",
  please: "sad_up",

  // Worried
  worried: "worried",
  concern: "worried",
  anxious: "worried",
  nervous: "worried",
  stress: "worried",

  // Focused/Determined
  focused: "focused",
  determined: "focused",
  concentrate: "focused",
  serious: "focused",
  thinking: "focused",

  // Annoyed
  annoyed: "annoyed",
  irritated: "annoyed",
  bothered: "annoyed",

  // Angry
  angry: "angry",
  mad: "angry",

  // Furious (extreme anger)
  furious: "furious",
  rage: "furious",
  hate: "furious",

  // Surprised
  surprised: "surprised",
  wow: "surprised",
  shock: "surprised",

  // Awe (amazed surprise)
  awe: "awe",
  incredible: "awe",
  unbelievable: "awe",
  omg: "awe",

  // Skeptic
  skeptic: "skeptic",
  doubt: "skeptic",
  suspicious: "skeptic",
  hmm: "skeptic",

  // Frustrated/Bored
  frustrated: "frustrated",
  bored: "frustrated",
  tired: "frustrated",

  // Unimpressed
  unimpressed: "unimpressed",
  meh: "unimpressed",
  whatever: "unimpressed",

  // Sleepy
  sleepy: "sleepy",
  sleep: "sleepy",
  drowsy: "sleepy",

  // Scared
  scared: "scared",
  afraid: "scared",
  fear: "scared",
  terrified: "scared",

  // Squint
  squint: "squint",
  narrow: "squint",

  // Blink
  blink: "blink_high",
  wink: "blink_low",
};

export const mapTextToEmotion = (text: string): EmotionType => {
  const lowerText = text.toLowerCase();

  // Check for exact matches first
  for (const [keyword, emotion] of Object.entries(EMOTION_KEYWORDS)) {
    if (lowerText.includes(keyword)) {
      return emotion;
    }
  }

  // Check for emotional intensity indicators
  if (
    lowerText.includes("very") ||
    lowerText.includes("extremely") ||
    lowerText.includes("super")
  ) {
    if (lowerText.includes("happy") || lowerText.includes("good"))
      return "glee";
    if (lowerText.includes("angry") || lowerText.includes("mad"))
      return "furious";
    if (lowerText.includes("surprised")) return "awe";
  }

  return "neutral";
};

export const getRandomEmotion = (): EmotionType => {
  const emotions: EmotionType[] = [
    "happy",
    "glee",
    "sad_down",
    "sad_up",
    "worried",
    "focused",
    "annoyed",
    "surprised",
    "skeptic",
    "frustrated",
    "unimpressed",
    "sleepy",
    "suspicious",
    "squint",
    "angry",
    "furious",
    "scared",
    "awe",
  ];
  return emotions[Math.floor(Math.random() * emotions.length)];
};

export const getEmotionIntensity = (emotion: EmotionType): number => {
  const intensityMap: Record<EmotionType, number> = {
    neutral: 0.3,
    blink_high: 0.1,
    blink_low: 0.1,
    happy: 0.7,
    glee: 1.0,
    sad_down: 0.6,
    sad_up: 0.5,
    worried: 0.4,
    focused: 0.8,
    annoyed: 0.6,
    surprised: 0.8,
    skeptic: 0.5,
    frustrated: 0.7,
    unimpressed: 0.3,
    sleepy: 0.2,
    suspicious: 0.6,
    squint: 0.4,
    angry: 0.8,
    furious: 1.0,
    scared: 0.9,
    awe: 0.9,
  };

  return intensityMap[emotion] || 0.5;
};
