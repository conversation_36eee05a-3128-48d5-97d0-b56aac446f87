import { EmotionType } from "@/utils/emotionTypes";

// Web Audio API based sound generation
class SoundGenerator {
  private audioContext: AudioContext | null = null;

  constructor() {
    if (typeof window !== "undefined") {
      this.audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
    }
  }

  private async ensureAudioContext() {
    if (!this.audioContext) return null;

    if (this.audioContext.state === "suspended") {
      await this.audioContext.resume();
    }

    return this.audioContext;
  }

  private createBeep(
    frequency: number,
    duration: number,
    type: OscillatorType = "sine"
  ) {
    return new Promise<void>(async (resolve) => {
      const ctx = await this.ensureAudioContext();
      if (!ctx) {
        resolve();
        return;
      }

      const oscillator = ctx.createOscillator();
      const gainNode = ctx.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(ctx.destination);

      oscillator.frequency.setValueAtTime(frequency, ctx.currentTime);
      oscillator.type = type;

      gainNode.gain.setValueAtTime(0, ctx.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.1, ctx.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(
        0.001,
        ctx.currentTime + duration
      );

      oscillator.start(ctx.currentTime);
      oscillator.stop(ctx.currentTime + duration);

      oscillator.onended = () => resolve();
    });
  }

  private async playSequence(
    notes: { freq: number; duration: number; type?: OscillatorType }[]
  ) {
    for (const note of notes) {
      await this.createBeep(note.freq, note.duration, note.type);
      await new Promise((resolve) => setTimeout(resolve, 50)); // Small gap between notes
    }
  }

  async playEmotionSound(emotion: EmotionType) {
    try {
      switch (emotion) {
        case "happy":
          await this.playSequence([
            { freq: 440, duration: 0.2 },
            { freq: 554, duration: 0.2 },
            { freq: 659, duration: 0.3 },
          ]);
          break;

        case "glee":
          await this.playSequence([
            { freq: 523, duration: 0.15 },
            { freq: 659, duration: 0.15 },
            { freq: 784, duration: 0.15 },
            { freq: 1047, duration: 0.25 },
          ]);
          break;

        case "sad_down":
        case "sad_up":
          await this.playSequence([
            { freq: 330, duration: 0.4 },
            { freq: 294, duration: 0.4 },
            { freq: 262, duration: 0.5 },
          ]);
          break;

        case "angry":
          await this.playSequence([
            { freq: 220, duration: 0.3, type: "square" },
            { freq: 185, duration: 0.3, type: "square" },
          ]);
          break;

        case "furious":
          await this.playSequence([
            { freq: 150, duration: 0.2, type: "sawtooth" },
            { freq: 130, duration: 0.2, type: "sawtooth" },
            { freq: 110, duration: 0.3, type: "sawtooth" },
          ]);
          break;

        case "surprised":
          await this.createBeep(800, 0.1);
          await new Promise((resolve) => setTimeout(resolve, 100));
          await this.createBeep(1200, 0.2);
          break;

        case "awe":
          await this.playSequence([
            { freq: 400, duration: 0.1 },
            { freq: 600, duration: 0.1 },
            { freq: 800, duration: 0.1 },
            { freq: 1000, duration: 0.3 },
          ]);
          break;

        case "scared":
          await this.playSequence([
            { freq: 800, duration: 0.1, type: "triangle" },
            { freq: 600, duration: 0.1, type: "triangle" },
            { freq: 400, duration: 0.2, type: "triangle" },
          ]);
          break;

        case "worried":
          await this.playSequence([
            { freq: 350, duration: 0.2 },
            { freq: 320, duration: 0.2 },
            { freq: 350, duration: 0.2 },
          ]);
          break;

        case "focused":
          await this.createBeep(500, 0.4, "triangle");
          break;

        case "sleepy":
          await this.playSequence([
            { freq: 300, duration: 0.5 },
            { freq: 250, duration: 0.6 },
            { freq: 200, duration: 0.7 },
          ]);
          break;

        case "annoyed":
          await this.playSequence([
            { freq: 400, duration: 0.15, type: "square" },
            { freq: 350, duration: 0.15, type: "square" },
            { freq: 400, duration: 0.15, type: "square" },
          ]);
          break;

        case "frustrated":
          await this.playSequence([
            { freq: 250, duration: 0.2, type: "square" },
            { freq: 230, duration: 0.2, type: "square" },
            { freq: 210, duration: 0.3, type: "square" },
          ]);
          break;

        case "skeptic":
          await this.createBeep(450, 0.3);
          await new Promise((resolve) => setTimeout(resolve, 100));
          await this.createBeep(400, 0.2);
          break;

        case "suspicious":
          await this.playSequence([
            { freq: 300, duration: 0.2 },
            { freq: 350, duration: 0.1 },
            { freq: 300, duration: 0.2 },
          ]);
          break;

        case "unimpressed":
          await this.createBeep(300, 0.5, "triangle");
          break;

        case "squint":
          await this.createBeep(400, 0.3, "triangle");
          break;

        case "blink_high":
        case "blink_low":
          await this.createBeep(600, 0.1);
          break;

        case "neutral":
        default:
          await this.createBeep(440, 0.2);
          break;
      }
    } catch (error) {
      console.error("Error playing sound:", error);
    }
  }

  async playStartupSound() {
    await this.playSequence([
      { freq: 262, duration: 0.2 },
      { freq: 330, duration: 0.2 },
      { freq: 392, duration: 0.2 },
      { freq: 523, duration: 0.4 },
    ]);
  }

  async playShakeSound() {
    await this.playSequence([
      { freq: 800, duration: 0.1 },
      { freq: 600, duration: 0.1 },
      { freq: 800, duration: 0.1 },
    ]);
  }

  async playCaptureSound() {
    await this.playSequence([
      { freq: 1000, duration: 0.1 },
      { freq: 1200, duration: 0.1 },
      { freq: 800, duration: 0.2 },
    ]);
  }
}

// Singleton instance
let soundGenerator: SoundGenerator | null = null;

export const getSoundGenerator = (): SoundGenerator => {
  if (!soundGenerator) {
    soundGenerator = new SoundGenerator();
  }
  return soundGenerator;
};

export const playEmotionSound = async (emotion: EmotionType) => {
  const generator = getSoundGenerator();
  await generator.playEmotionSound(emotion);
};

export const playStartupSound = async () => {
  const generator = getSoundGenerator();
  await generator.playStartupSound();
};

export const playShakeSound = async () => {
  const generator = getSoundGenerator();
  await generator.playShakeSound();
};

export const playCaptureSound = async () => {
  const generator = getSoundGenerator();
  await generator.playCaptureSound();
};
