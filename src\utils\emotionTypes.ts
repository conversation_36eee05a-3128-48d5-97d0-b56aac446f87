export type EmotionType =
  | "neutral"
  | "blink_high"
  | "happy"
  | "glee"
  | "blink_low"
  | "sad_down"
  | "sad_up"
  | "worried"
  | "focused"
  | "annoyed"
  | "surprised"
  | "skeptic"
  | "frustrated"
  | "unimpressed"
  | "sleepy"
  | "suspicious"
  | "squint"
  | "angry"
  | "furious"
  | "scared"
  | "awe";

export interface EmotionState {
  current: EmotionType;
  isBlinking: boolean;
  intensity: number; // 0-1 for animation intensity
}

export const EMOTIONS: Record<EmotionType, string> = {
  neutral: "Neutral",
  blink_high: "Blink (high)",
  happy: "Happy",
  glee: "Glee",
  blink_low: "Blink (low)",
  sad_down: "Sad (down)",
  sad_up: "Sad (up)",
  worried: "Worried",
  focused: "Focused",
  annoyed: "Annoyed",
  surprised: "Surprised",
  skeptic: "Skeptic",
  frustrated: "Frustrated",
  unimpressed: "Unimpressed",
  sleepy: "Sleepy",
  suspicious: "Suspicious",
  squint: "Squint",
  angry: "Angry",
  furious: "Furious",
  scared: "Scared",
  awe: "Awe",
};

// Emotion categories for easier grouping
export const EMOTION_CATEGORIES = {
  positive: ["happy", "glee", "awe"],
  negative: ["sad_down", "sad_up", "angry", "furious", "scared", "frustrated"],
  neutral: ["neutral", "focused", "sleepy"],
  reactive: [
    "surprised",
    "worried",
    "annoyed",
    "skeptic",
    "unimpressed",
    "suspicious",
  ],
  blink: ["blink_high", "blink_low"],
  special: ["squint"],
} as const;
